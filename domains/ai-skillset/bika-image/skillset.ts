import type { SkillsetHandler } from '../types';
import tools from './server';

const skillsetHandler: SkillsetHandler = async (ctx) => ({
  key: 'bika-image',
  display: {
    label: 'Bika Image',
    description: 'Image processing tools, like image generation, image editing, and more',
    hidden: true,
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/image.png',
  },
  toolsetHandler: () => tools(ctx),
});

export default skillsetHandler;
