import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>Part } from 'ai';
import React from 'react';
import type { Skillset<PERSON>Map, Tool<PERSON>ComponentProps } from '../types';

const toolsetUI: SkillsetUIMap = {
  askForConfirmation: {
    artifact: (props: { toolInvocation: ToolUIPart | DynamicToolUIPart }) => (
      <div>askForConfirmation: {JSON.stringify(props.toolInvocation)}</div>
    ),
    component: (props: Tool<PERSON>ComponentProps) => (
      <div>
        askForConfirmation: {JSON.stringify(props.toolInvocation)}
        <button type="button" onClick={props.onClickTool}>
          Click Tool
        </button>
      </div>
    ),
  },
  getWeatherInformation: {
    artifact: (props: { toolInvocation: ToolUIPart | DynamicToolUIPart }) => (
      <div>getWeatherInformation: {JSON.stringify(props.toolInvocation)}</div>
    ),
    component: (props: Tool<PERSON>ComponentProps) => (
      <div>
        getWeatherInformation: {JSON.stringify(props.toolInvocation)}
        <button type="button" onClick={props.onClickTool}>
          Click Tool
        </button>
      </div>
    ),
  },
};

export default toolsetUI;
