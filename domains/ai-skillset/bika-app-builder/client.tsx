'use client';

// import { TOOLS_CONFIG } from '@bika/contents/config/client/ai/ai-consulting-tools-display';
// import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import type { NodeCreateDTO } from '@bika/types/node/dto';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import assert from 'assert';
import type {
  ClientExecuteContext,
  SkillsetUIMap,
  // ToolUIComponentProps
} from '../types';

const toolsetUI: SkillsetUIMap = {
  // 'bika-ai-app-builder-space-id': {
  //   displayName: 'Confirm Space Id',
  //   artifact: undefined,
  //   // 通过 trpc 获取所有空间站，生成选择框，点击后，执行 addToolResult 获得 { spaceId: string }
  //   component: () => <>For Space Id select</>,
  //   clientExecute: undefined,
  // },
  'bika-ai-app-builder-planner': {
    displayName: 'Agent Planner',
    artifact: 'server-artifact',
    component: undefined, // consultingComponent,
  },
  'bika-ai-app-builder-engineer': {
    displayName: 'Agent Engineer',
    artifact: 'server-artifact',
    component: undefined,
  },
  'bika-ai-app-builder-installer': {
    displayName: 'Agent Installer',
    clientExecute: async (
      toolInvocation: ToolUIPart | DynamicToolUIPart,
      context?: ClientExecuteContext,
    ) => {
      if (toolInvocation.state !== 'input-available') {
        throw new Error('Tool invocation state is not call');
      }
      if (!context || !context.apiCaller) {
        throw new Error('Context is required');
      }
      const { engineerArtifactId, spaceId } = toolInvocation.input as {
        engineerArtifactId: string;
        spaceId: string;
      };
      if (!engineerArtifactId || !spaceId) {
        throw new Error('Artifact id and space id are required');
      }
      const { trpc } = context.apiCaller;

      const [artifact, rootNode] = await Promise.all([
        trpc.ai.fetchArtifact.query({
          artifactId: engineerArtifactId,
        }),
        trpc.space.getRootNode.query({
          spaceId,
        }),
      ]);
      assert(artifact.type === 'ai-agent', 'artifact type must be ai-agent');

      const createDTO: NodeCreateDTO = {
        spaceId,
        parentId: rootNode.id,
        data: {
          ...artifact.data,
          bo: artifact.data,
        },
      };
      console.log('agent createDTO', createDTO);
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 0,
      });
      const newNode = await trpc.node.create.mutate(createDTO);
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 1,
        redirectNodeId: newNode.id,
      });
    },
    component: () => ({
      isAsk: true,
    }),
  },

  // 'ai-consulting-consultant': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ai-consulting-business-analyst': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ai-consulting-designer': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ai-consulting-data-scientist': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ai-consulting-engineer': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ai-consulting-marketer': {
  //   artifact: 'server-artifact',
  //   component: consultingComponent,
  // },
  // 'ask-for-install': {
  //   component: () => ({
  //     isAsk: true,
  //   }),
  // },
  // 'do-install': {
  //   clientExecute: undefined,
  //   component: undefined,
  // },
};

export default toolsetUI;
