import type { SkillsetHand<PERSON> } from '../types';
import tools from './server';

const skillsetHandler: SkillsetHandler = async (ctx) => ({
  key: 'bika-super-agent',
  display: {
    label: 'Bika Super Agent',
    description: 'Bika Super Agent for space Chief of Staff',
    hidden: true,
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/super_agent.png',
  },
  toolsetHandler: () => tools(ctx),
});

export default skillsetHandler;
