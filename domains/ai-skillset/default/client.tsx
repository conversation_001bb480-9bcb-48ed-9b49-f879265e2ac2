'use client';

import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import { AIIntentUIVOSchemas } from '@bika/types/ai/vo';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import React from 'react';
import { AIWizardIntentUIArea } from '../../ai/client/wizard/ai-wizard-intent-ui-area';
import type { SkillsetUIMap } from '../types';

export const CONST_DEFAULT_SKILL_NODE_ICON: INodeIconValue = {
  kind: 'avatar',
  avatar: {
    type: 'PRESET',
    url: '/assets/ai/skillset/default.png',
  },
};

const toolsetUI: SkillsetUIMap = {
  /**
   *
   * @deprecated 老的组件，不推荐使用，改用 ai-consulting-quick skillsets
   */
  // 'ai-consulting-deprecated': {
  //   artifact: (props: ArtifactUIProps) => {
  //     if (props.toolInvocation.args?.consultingType === 'engineer') {
  //       return <NodeResourcesArtifact resources={props.toolInvocation.args?.resources} />;
  //     }
  //     if (props.toolInvocation.args?.consultingType === 'marketer') {
  //       return <HtmlArtifact content={props.toolInvocation.args?.content} />;
  //     }
  //     if (['business-analyst', 'consultant'].includes(props.toolInvocation.args?.consultingType)) {
  //       return <MarkdownArtifact content={props.toolInvocation.args?.content} />;
  //     }
  //     return <>{props.toolInvocation.args?.content}</>;
  //   },
  //   component: undefined,
  // },
  form: {
    component: (props: {
      toolInvocation: ToolUIPart | DynamicToolUIPart;
      onClick?: () => void;
      sendMessage?: (msg: string) => Promise<void>;
      sendUI?: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
    }) => {
      const args = props.toolInvocation.input;
      const intentUIVO = AIIntentUIVOSchemas.parse(args);
      return (
        <>
          <AIWizardIntentUIArea
            disabled={false}
            intentUI={intentUIVO}
            loading={false}
            resolutionStatus={''}
            type={''}
            sendUI={props.sendUI!}
            sendMessage={props.sendMessage!}
            close={async () => {}}
          />
        </>
      );
    },
  },
  load_attachment: {
    artifact: 'server-artifact',
    component: undefined,
  },
  // [ImageSkillsetName.image_to_text]: {
  //   artifact: 'server-artifact',
  //   component: () => ({}),
  // },
  read_node_content: {
    artifact: 'server-artifact',
  },
};

export default toolsetUI;
