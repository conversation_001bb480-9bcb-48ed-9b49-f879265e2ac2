'use client';

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ToolUIPart } from 'ai';
import type { ClientExecuteContext, SkillsetUIMap } from '../types';

const toolsetUI: SkillsetUIMap = {
  search_image: {
    artifact: undefined,
    component: undefined, // 则使用默认
  },
  generate_html_page: {
    artifact: 'server-artifact',
    component: undefined, // 则使用默认

    clientExecute: undefined,
  },

  ask_for_apply_ai_page: {
    artifact: undefined,
    component: () => ({
      isAsk: true,
    }),
    clientExecute: async (
      toolInvocation: ToolUIPart | DynamicToolUIPart,
      context?: ClientExecuteContext,
    ) => {
      if (toolInvocation.state !== 'input-available') {
        throw new Error('Tool invocation state is not call');
      }
      if (!context || !context.apiCaller) {
        throw new Error('Context is required');
      }
      const { artifactId, nodeId } = toolInvocation.input as { artifactId: string; nodeId: string };
      if (!artifactId || !nodeId) {
        throw new Error('Artifact id and node id are required');
      }
      const { trpc } = context.apiCaller;
      const [artifact, node] = await Promise.all([
        trpc.ai.fetchArtifact.query({ artifactId }),
        trpc.node.info.query({ id: nodeId }),
      ]);
      if (!artifact) {
        throw new Error('Artifact not found');
      }
      if (!node) {
        throw new Error('Node not found');
      }

      const html = (artifact.data as { html: string }).html;

      await context.updateNode({
        id: nodeId,
        spaceId: node.spaceId,
        data: {
          resourceType: 'PAGE',
          bo: {
            data: {
              kind: 'SINGLE_HTML',
              content: html,
            },
          },
        },
      });

      console.log('update page success', nodeId);
    },
  },
  // do_apply_ai_page: {
  //   component: () => ({
  //     result: {
  //       name: 'Apply AI Page',
  //       description: 'The AI page has been applied successfully.',
  //     },
  //   }),
  // },
};

export default toolsetUI;
