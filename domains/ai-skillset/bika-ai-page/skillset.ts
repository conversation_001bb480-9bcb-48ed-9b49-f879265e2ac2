import type { SkillsetHandler } from '../types';
import tools from './server';

const skillsetHandler: SkillsetHandler = async (ctx) => ({
  key: 'bika-ai-page',
  display: {
    label: 'Bika AI Page',
    description: 'Bika AI Page for building AI-powered HTML web pages and applications',
    hidden: true,
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/aipage.png',
  },
  toolsetHandler: () => tools(ctx),
});

export default skillsetHandler;
