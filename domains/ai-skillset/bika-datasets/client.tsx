'use client';

import { GridArtifact } from '@bika/domains/ai/client/chat/artifacts/grid-artifact';
import type { ListRecordToolResult } from '@bika/domains/ai/server/types';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import type { WebsetItem } from 'exa-js';
import _ from 'lodash';
import type { SkillsetUIMap } from '../types';
import { buildGridArtifactData } from './helper';

const fetchGridArtifactData = (
  toolInvocation: ToolUIPart | DynamicToolUIPart,
  entityType: 'company' | 'person',
): ListRecordToolResult => {
  const result = _.get(toolInvocation, 'output');

  if (!result) {
    return { isError: true, fields: [], records: [] };
  }

  if (Array.isArray(result)) {
    return buildGridArtifactData(result as WebsetItem[], entityType);
  }

  return result as ListRecordToolResult;
};

const DatasetToolsetUI: SkillsetUIMap = {
  companies: {
    artifact: ({ toolInvocation }) => {
      const data = fetchGridArtifactData(toolInvocation, 'company');
      return <GridArtifact tool={toolInvocation} data={data} />;
    },
    component: undefined,
  },
  people: {
    artifact: ({ toolInvocation }) => {
      const data = fetchGridArtifactData(toolInvocation, 'person');
      return <GridArtifact tool={toolInvocation} data={data} />;
    },
    component: undefined,
  },
};

export default DatasetToolsetUI;
