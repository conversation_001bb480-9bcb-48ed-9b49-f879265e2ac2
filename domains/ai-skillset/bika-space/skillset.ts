import type { SkillsetHandler } from '../types';
import tools from './server';

const skillsetHandler: SkillsetHandler = async (ctx) => ({
  key: 'bika-space',
  display: {
    label: 'Bika Space',
    description: 'Bika Space for managing spaces, projects, and teams',
    hidden: true,
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/space.png',
  },
  toolsetHandler: () => tools(ctx),
});

export default skillsetHandler;
