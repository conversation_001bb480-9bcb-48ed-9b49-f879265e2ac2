import { useLocale } from '@bika/contents/i18n/context';
import type { NodeResourceType } from '@bika/types/node/bo';
import { Link } from '@bika/ui/form-components';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import React from 'react';
import {
  DefaultToolRenderer,
  getDefaultToolUIContentProps,
} from '../../../ai/client/chat/tools/default-tool-renderer';
import type { ToolUIComponentProps } from '../../types';

type IResult = { id: string; name: string; description?: string; type: NodeResourceType };
export const NodeSourceComponent: React.FC<
  ToolUIComponentProps & { title?: string; toolInvocation: ToolUIPart | DynamicToolUIPart }
> = ({ skillsets, executeToolResult, addToolResult, toolInvocation, title }) => {
  const { t, i } = useLocale();
  const localeContext = useLocale();
  const [showSource, setShowSource] = React.useState(true);
  const [result, setResult] = React.useState<IResult[] | undefined>();

  const defaultToolContentProps = getDefaultToolUIContentProps(toolInvocation, {
    locale: localeContext,
  });

  React.useEffect(() => {
    if (toolInvocation.state === 'output-available') {
      if (Array.isArray(toolInvocation.output)) {
        setResult(toolInvocation.output);
      } else {
        setResult([toolInvocation.output as IResult]);
      }
    }
  }, [toolInvocation]);

  return (
    <>
      {title && (
        <DefaultToolRenderer
          addToolResult={addToolResult}
          executeToolResult={executeToolResult}
          skillsets={skillsets}
          contentProps={defaultToolContentProps}
          toolInvocation={toolInvocation}
          localeContext={localeContext}
        />
      )}
      {result && (
        <Stack mb={1}>
          <Stack
            sx={{
              display: 'flex',
              cursor: 'pointer',
              flexDirection: 'row',
              justifyContent: 'center',
              width: 'max-content',
              borderRadius: '8px',
              p: 1,
              color: 'var(--text-primay)',
              backgroundColor: 'var(--bg-controls)',
              maxWidth: '80%',
              '&:hover': {
                backgroundColor: 'var(--bg-controls-hover)',
              },
            }}
            onClick={() => {
              setShowSource(!showSource);
            }}
          >
            <Typography mr={1} textColor="var(--text-secondary)" level="b4">
              {t('ai.reference', { count: result.length || 0 })}
            </Typography>
            {showSource ? (
              <ChevronUpOutlined color={'var(--text-secondary)'} />
            ) : (
              <ChevronDownOutlined color={'var(--text-secondary)'} />
            )}
          </Stack>
          {showSource && (
            <Stack>
              {result.map(
                (
                  part: { id: string; name: string; description?: string; type: NodeResourceType },
                  idx2: number,
                ) => (
                  <Link my={0.5} key={idx2} target="_blank" href={part.id}>
                    {i(part.name)}
                  </Link>
                ),
              )}
            </Stack>
          )}
        </Stack>
      )}
    </>
  );
};
