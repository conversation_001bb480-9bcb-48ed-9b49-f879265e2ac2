import { useLocale } from '@bika/contents/i18n';
import type { NodeResource } from '@bika/types/node/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { Box } from '@bika/ui/layouts';
import { useTheme } from '@mui/joy/styles';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import dynamic from 'next/dynamic';
import React from 'react';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';

// FlowEditor to display the workflow
const FlowEditor = dynamic(
  () => import('@bika/ui/editor/flow-editor/flow-editor').then((module) => module.FlowEditor),
  {
    loading: () => <>loading...</>,
    ssr: false,
  },
);

interface EngineerArtifactProps {
  resources: NodeResource[];
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolUIPart | DynamicToolUIPart;
  expandable?: boolean;
}

export const NodeResourcesArtifact = (props: EngineerArtifactProps) => {
  const { resources, skillsets = [], tool, expandable = true } = props;
  const theme = useTheme();
  const { t } = useLocale();

  return (
    <ArtifactContainer
      data={resources}
      skillsets={skillsets}
      tool={tool}
      expandable={expandable}
      rowDataType="json"
      switchProps={{
        rowDataLabel: t.ai.artifact_template_code,
        previewLabel: t.ai.artifact_workflow,
      }}
    >
      <Box flex={1} display={'flex'} sx={{ height: '100%' }}>
        <FlowEditor
          showControl
          theme={theme.palette.mode}
          data={{
            type: 'node-resources',
            resources,
          }}
        />
      </Box>
    </ArtifactContainer>
  );
};
