import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n';
import type { FieldVO, RecordVO } from '@bika/types/database/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { Box } from '@bika/ui/layouts';
import { useTheme } from '@mui/joy/styles';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import _ from 'lodash';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';
import { RecordVOComponent } from '../../database/client/record-detail/record-detail-component';
import { useDatabaseMeta } from '../../database/client/record-detail/use-database-meta';

export const RecordDetailComponent = (props: { data: RecordVO }) => {
  const theme = useTheme();
  console.log('theme', theme.palette.mode);

  const databaseMeta = useDatabaseMeta(props.data.databaseId || '');
  const fieldList: FieldVO[] = databaseMeta?.columns ?? [];

  return <RecordVOComponent data={props.data} fieldList={fieldList} />;
};

interface RecordArtifactProps {
  skillsets: SkillsetSelectDTO[];
  tool: ToolUIPart | DynamicToolUIPart;
}

export const RecordArtifact = (props: RecordArtifactProps) => {
  const { skillsets, tool } = props;
  const trpcQuery = useTRPCQuery();
  const { t } = useLocale();

  const recordId: string | undefined =
    tool.state === 'output-available' ? _.get(tool.output, 'recordId') : undefined;
  const databaseId: string | undefined =
    tool.state === 'output-available' ? _.get(tool.input, 'databaseId') : undefined;

  const { data: record } = trpcQuery.database.getRecord.useQuery(
    {
      databaseId: databaseId!,
      recordId: recordId!,
    },
    {
      enabled: recordId !== undefined && databaseId !== undefined,
    },
  );
  const data = (
    props.tool.state === 'input-available' ? tool.input : _.merge(tool.input, tool.output)
  ) as object;
  return (
    <ArtifactContainer
      expandable={false}
      rowDataOnly={props.tool.state === 'input-available'}
      data={data}
      tool={tool}
      skillsets={skillsets}
      switchProps={{
        rowDataLabel: t.ai.artifact_code,
        previewLabel: t.ai.artifact_preview,
      }}
    >
      <Box className="p-[12px] w-full h-full overflow-y-auto">
        {record?.record && (
          <RecordVOComponent data={record.record as RecordVO} fieldList={record.fields} />
        )}
      </Box>
    </ArtifactContainer>
  );
};
