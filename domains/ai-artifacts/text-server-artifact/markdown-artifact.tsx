import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { Icon<PERSON>utton } from '@bika/ui/button-component';
import { CodeViewWithChildren } from '@bika/ui/code-view';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import { Markdown } from '@bika/ui/markdown';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import { saveAs } from 'file-saver';
import { throttle } from 'lodash';
import { useCallback, useEffect, useRef } from 'react';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';
import { markdownToHtml } from '../../shared/server/utils/convert';

interface MarkdownArtifactProps {
  // content: string;
  skillsets: SkillsetSelectDTO[];
  tool: ToolUIPart | DynamicToolUIPart;
  value: {
    content: string;
    title: string;
  };
}

// user can download------------------------`
export const saveMarkdownAsFile = async (fileName: string, content: string) => {
  // Create a blob with the markdown content
  const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });

  // Ensure the filename has .md extension
  const finalFileName = fileName.endsWith('.md') ? fileName : `${fileName}.md`;

  saveAs(blob, finalFileName);
};

export const MarkdownArtifact = (props: MarkdownArtifactProps) => {
  const { skillsets, tool, value } = props;
  const data = value?.content ?? '';
  const title = value?.title ?? '';
  const bottomRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(
    throttle(() => {
      console.log('bottomRef.current', bottomRef.current);
      bottomRef.current?.scrollIntoView({ behavior: 'auto', block: 'end' });
    }, 100),
    [],
  );

  useEffect(() => {
    scrollToBottom();
  }, [data, scrollToBottom]);

  useEffect(
    () => () => {
      scrollToBottom.cancel();
    },
    [scrollToBottom],
  );

  // Create the download button component
  const downloadButton = (
    <>
      <IconButton
        variant="plain"
        size="sm"
        color="neutral"
        onClick={() => {
          saveMarkdownAsFile(title || 'Document', data);
        }}
        sx={{
          '&:hover': {
            backgroundColor: 'var(--hover)',
          },
        }}
      >
        <DownloadOutlined color="var(--text-primary)" />
      </IconButton>
    </>
  );

  return (
    <ArtifactContainer
      data={data}
      skillsets={skillsets}
      tool={tool}
      rowDataType="markdown"
      title={title}
      toolbarButton={downloadButton}
    >
      <CodeViewWithChildren title={title} code={markdownToHtml(data)}>
        <div
          className="p-[16px] m-2"
          style={{
            overflowY: 'auto',
            paddingLeft: '16px',
            paddingRight: '16px',
            paddingTop: '16px',
            paddingBottom: '16px',
          }}
        >
          <Markdown markdown={data} />
          <div ref={bottomRef} />
        </div>
      </CodeViewWithChildren>
    </ArtifactContainer>
  );
};
