import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useTheme } from '@mui/joy/styles';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import React from 'react';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';

interface HtmlArtifactProps {
  content: string;
  skillsets: SkillsetSelectDTO[];
  tool: ToolUIPart | DynamicToolUIPart;
  data: { html?: string };
}

export const HtmlArtifact = (props: HtmlArtifactProps) => {
  const theme = useTheme();
  const { content, skillsets, tool, data } = props;

  return (
    <ArtifactContainer data={data?.html ?? ''} skillsets={skillsets} tool={tool} rowDataType="html">
      <iframe
        title="HTML Artifact Preview"
        srcDoc={`
          <!DOCTYPE html>
          <html data-theme="${theme.palette.mode}">
            <head>
              <style>
                :root {
                  color-scheme: ${theme.palette.mode};
                }
              </style>
            </head>
            <body>
              ${content}
            </body>
          </html>
        `}
        width="100%"
        height="100%"
        style={{ border: 'none' }}
      />
    </ArtifactContainer>
  );
};
