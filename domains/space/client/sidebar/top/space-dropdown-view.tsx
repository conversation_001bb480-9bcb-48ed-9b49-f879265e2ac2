'use client';

import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { SwitchSpace } from '@bika/domains/space/client/sidebar/switch-space/index';
import { useSpace<PERSON>ontext<PERSON><PERSON>ce, useSpaceRouter } from '@bika/types/space/context';
import type { SpaceRenderVO } from '@bika/types/space/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { Button } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { Popover, PopoverContent, PopoverTrigger } from '@bika/ui/components/popover/index';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import React, { useMemo } from 'react';

const SPACE_PAGE_SIZE = 20;

export function SpaceDropdownView() {
  const spaceContext = useSpaceContextForce();
  const globalContext = useGlobalContext();
  const {
    data: { id: spaceId, name },
    permission,
  } = spaceContext;
  const router = useSpaceRouter();
  const { logo } = spaceContext.data;
  const { trpcQuery, trpc } = useApiCaller();
  const { t } = useLocale();

  const [pageNo, setPageNo] = React.useState<number>(1);

  const { data } = trpcQuery.space.list.useQuery({
    pageNo: 1,
    pageSize: SPACE_PAGE_SIZE,
  });

  const [isMore, setIsMore] = React.useState<boolean>(true);
  const [moreLoading, setMoreLoading] = React.useState<boolean>(false);
  const [spaces, setSpaces] = React.useState<SpaceRenderVO[]>([]);

  const isAdmin: boolean = useMemo(
    () => (permission ? Object.values(permission).some((value) => value === true) : false),
    [permission],
  );

  const onSetting = () => {
    spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_SETTING' } });
  };

  React.useEffect(() => {
    if (data) {
      setSpaces(data);
      const hasMore = data.length === SPACE_PAGE_SIZE;
      setIsMore(hasMore);
    } else {
      setIsMore(false);
    }
  }, [data]);

  return (
    <Popover>
      <PopoverTrigger asChild={true}>
        <div className="w-full flex items-center justify-between flex-row cursor-pointer rounded-md gap-4">
          <div className="flex flex-col items-center flex-row w-full gap-1">
            <AvatarImg
              name={name || ''}
              customSize={AvatarSize.Size40}
              avatar={logo}
              shape={'SQUARE'}
            />
            <div className="flex items-center flex-row gap-1 rounded-[4px] px-2 py-1.5 overflow-hidden hover:bg-[var(--hover)]">
              {/* <Typography
                level="h7"
                textColor={'var(--text-primary)'}
                sx={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}
              >
                {name}
              </Typography> */}
              <ChevronDownOutlined color={'var(--text-primary)'} size={12} />
            </div>
          </div>
          {/* <IconButton
            sx={{
              '&:hover': {
                backgroundColor: 'var(--hover)',
              },
            }}
            onClick={(e) => {
              e.stopPropagation();

              spaceContext.showUIModal({
                type: 'ai-commander',
              });
            }}
          >
            <SearchOutlined color={'var(--text-primary)'} size={16} />
          </IconButton> */}
        </div>
      </PopoverTrigger>
      <PopoverContent className="Popover">
        <SwitchSpace
          more={
            isMore && (
              <div className="text-center my-2">
                <Button
                  variant="plain"
                  color="neutral"
                  sx={{
                    fontWeight: 400,
                  }}
                  disabled={!isMore}
                  loading={moreLoading}
                  onClick={async () => {
                    setMoreLoading(true);
                    const newPageNo = pageNo + 1;
                    const newData = await trpc.space.list.query({
                      pageNo: newPageNo,
                      pageSize: SPACE_PAGE_SIZE,
                    });
                    setPageNo(pageNo + 1);
                    if (newData && newData.length > 0) {
                      setSpaces([...spaces, ...newData]);
                      const hasMore = newData.length === SPACE_PAGE_SIZE;
                      setIsMore(hasMore);
                    } else {
                      setIsMore(false);
                    }
                    setMoreLoading(false);
                  }}
                >
                  {moreLoading ? t.pagination.loading : t.pagination.load_more}
                </Button>
              </div>
            )
          }
          name={name ?? 'UnNamed'}
          {...(isAdmin ? { onSetting } : {})}
          list={spaces.map((space) => ({
            id: space.id,
            name: space.name,
            selected: space.id === spaceId,
            messageCount: space.unreadMessageCount,
            logo: space.logo,
            memberCount: space.memberCount - space.aiMembersCount,
            planName: space.subscription?.planName ?? 'FREE',
          }))}
          onCreate={() => {
            globalContext.showUIModal({
              name: 'CREATE_SPACE',
            });
          }}
          onNavigate={(id: string) => {
            router.push(`/space/${id}`);
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
