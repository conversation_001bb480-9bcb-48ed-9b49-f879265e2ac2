import type { NodeCreateDTOWithTemplateName } from '@bika/domains/ai-skillset/types';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import { create } from 'zustand';

export const useBuildInstallGlobalStore = create<{
  data?: {
    toolInvocation: ToolUIPart | DynamicToolUIPart | null;
    dto: NodeCreateDTOWithTemplateName[];
    redirectNodeId?: string;
    index?: number;
    process: number;
  };
  setData: (data?: {
    index?: number;
    toolInvocation: ToolUIPart | DynamicToolUIPart | null;
    dto: NodeCreateDTOWithTemplateName[];
    redirectNodeId?: string;
    process: number;
  }) => void;
}>((set) => ({
  data: undefined,
  setData: (data) => set(() => ({ data })),
}));
