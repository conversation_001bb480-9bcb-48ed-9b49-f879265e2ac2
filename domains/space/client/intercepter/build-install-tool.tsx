import { getResourcesTypesConfig } from '@bika/contents/config/client/node/node-resources';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceId } from '@bika/types/space/context';
import { IconButton } from '@bika/ui/button';
import { Link } from '@bika/ui/form-components';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import BuilderOutlined from '@bika/ui/icons/doc_hide_components/builder_outlined';
import CheckCircleColorFilled from '@bika/ui/icons/doc_hide_components/check_circle_color_filled';
import { Box, Stack } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { useMemo } from 'react';
import { useBuildInstallGlobalStore } from './use-build-install-global-store';

export { useBuildInstallGlobalStore };
export const BuildInstallTool = () => {
  const { data, setData } = useBuildInstallGlobalStore();
  const { i, t } = useLocale();
  const localeContext = useLocale();
  const spaceId = useSpaceId();
  const process = data?.process || 0;

  const dtoTemplateName = data?.index != null ? data?.dto?.[data?.index]?.templateName : undefined;

  const templateName = useMemo(() => {
    const folder = data?.dto?.find((item) => item.data.resourceType === 'FOLDER');
    return i(folder?.data.name || '');
  }, [data?.dto, i]);

  const redirectUrl = data?.redirectNodeId
    ? `/space/${spaceId}/node/${data?.redirectNodeId}`
    : undefined;

  const resourceConfig = useMemo(() => getResourcesTypesConfig(localeContext), [localeContext]);
  // 获取当前资源类型的显示名称
  const currentResourceTypeName = useMemo(() => {
    if (data?.index == null) {
      return t.launcher.node;
    }
    const currentItem = data?.dto?.[data?.index];
    if (!currentItem?.data?.resourceType) {
      return t.launcher.node;
    }

    try {
      const resourceType = currentItem.data.resourceType;
      return resourceConfig[resourceType]?.label ?? '';
    } catch (_error) {
      return t.launcher.node;
    }
  }, [data?.dto, data?.index, i, t]);

  if (!data) {
    return null; // 如果没有数据，直接返回 null
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: '40px',
        right: '40px',
        zIndex: 1900,
        border: '1px solid var(--border-default)',
        backgroundColor: 'var(--bg-popup)',
        boxShadow: 'var(--shadow-high)',
        borderRadius: '8px',
        width: '400px',
      }}
    >
      <IconButton
        sx={{
          position: 'absolute',
          top: '8px',
          right: '8px',
          zIndex: 1901,
        }}
        onClick={() => {
          setData(undefined);
        }}
      >
        <CloseOutlined color="var(--text-primary)" />
      </IconButton>
      <Stack
        direction="row"
        alignItems="center"
        sx={{
          p: '8px 16px',
          gap: '8px',
          flex: 1,
          borderRadius: '8px',
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          borderBottom: '1px solid var(--border-default)',
        }}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{
            backgroundColor: 'var(--bg-controls)',
            borderRadius: '4px',
            width: '32px',
            height: '32px',
            flexShrink: 0,
            position: 'relative',
          }}
        >
          <BuilderOutlined size={20} color="var(--rainbowRed5)" />
        </Box>

        <Box sx={{ flex: 1, minWidth: 0, position: 'relative' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" gap={1}>
            <Box overflow={'hidden'} sx={{ flex: 1, minWidth: 0 }}>
              <Typography textColor={'var(--text-primary)'} level="b2">
                {t.wizard.installer}
              </Typography>
              <EllipsisText>
                <Typography textColor={'var(--text-secondary)'} level="b4" noWrap>
                  {t.wizard.agent_engineer}
                </Typography>
              </EllipsisText>
            </Box>
          </Stack>
        </Box>
      </Stack>
      <Box
        sx={{
          p: 2,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {process === 1 ? (
          <Box display="flex" alignItems="center" flexDirection="column" gap={1}>
            <CheckCircleColorFilled color="var(--status-success)" size={28} />
            <Typography textColor={'var(--text-primary)'} level="b2">
              {t.wizard.installation_completed}
              <Link
                href={redirectUrl}
                onClick={() => {
                  setData(undefined);
                }}
              >
                {t.wizard.installer_view_now}
              </Link>
            </Typography>
          </Box>
        ) : (
          <>
            <Typography textColor={'var(--text-primary)'} level="b2">
              {`${t.wizard.creating} ${currentResourceTypeName}:`}
            </Typography>
            <Stack direction="row" gap="8px">
              <Typography textColor={'var(--text-secondary)'} level="b4" noWrap>
                {t.wizard.creating}
              </Typography>

              <EllipsisText>
                <Typography textColor={'var(--brand)'} level="b4" noWrap>
                  {dtoTemplateName ?? templateName}
                </Typography>
              </EllipsisText>
            </Stack>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                width: '100%',
                height: '100%',
                background:
                  'linear-gradient(90deg, transparent 0%, var(--brand-light) 50%, transparent 100%)',
                animation: 'progressAnimation 2s infinite linear',
                pointerEvents: 'none',
                '@keyframes progressAnimation': {
                  '0%': {
                    left: '-100%',
                  },
                  '100%': {
                    left: '100%',
                  },
                },
              }}
            />
          </>
        )}
        {/* </Box> */}
      </Box>
    </Box>
  );
};
