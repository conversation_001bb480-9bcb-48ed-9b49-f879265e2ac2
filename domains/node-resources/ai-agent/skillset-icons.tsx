import type { SkillsetVO } from '@bika/types/skill/vo';
import { AvatarImg } from '@bika/ui/components/avatar/index';
import WrenchOutlined from '@bika/ui/icons/doc_hide_components/wrench_outlined';
import { Tooltip } from '@bika/ui/tooltip';
import { Box } from '@mui/joy';

interface SkillsetIconsProps {
  skillsets?: SkillsetVO[];
  maxDisplay?: number;
  disabled?: boolean;
  onEditClick?: () => void;
}

export function SkillsetIcons(props: SkillsetIconsProps) {
  const { skillsets = [], maxDisplay = 5, onEditClick, disabled } = props;

  // 去重处理：基于技能集的key进行去重
  const uniqueSkillsets = skillsets.filter(
    (skillset, index, self) => index === self.findIndex((s) => s.key === skillset.key),
  );

  // 当技能集数量超过maxDisplay时，显示前面的技能和一个"+N"指示器
  const hasMore = uniqueSkillsets.length > maxDisplay;
  const displaySkillsets = hasMore
    ? uniqueSkillsets.slice(0, maxDisplay - 1)
    : uniqueSkillsets.slice(0, maxDisplay);
  const remainingCount = uniqueSkillsets.length - (maxDisplay - 1);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        padding: '6px',
        border: '1px solid var(--border-default)',
        background: 'var(--bg-controls)',
        borderRadius: '6px',
        cursor: 'pointer',
        height: '32px', // Same height as New Chat button
        '&:hover': {
          backgroundColor: 'var(--bg-controls-hover)',
        },
        '&:active': {
          backgroundColor: 'var(--bg-controls-active)',
        },
      }}
      onClick={() => {
        if (disabled) {
          return;
        }
        onEditClick?.();
      }}
    >
      {/* wrench图标放在技能图标之前 */}
      <Tooltip title="Edit skillsets" placement="top">
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 20,
            height: 20,
          }}
        >
          <WrenchOutlined size={16} color="var(--text-secondary)" />
        </Box>
      </Tooltip>

      {/* 当有技能时显示分割线 */}
      {uniqueSkillsets.length > 0 && (
        <Box
          sx={{
            width: '1px',
            height: '16px',
            backgroundColor: 'var(--border-default)',
            margin: '0 2px',
          }}
        />
      )}

      {displaySkillsets.map((skillset) => (
        <Tooltip key={skillset.key} title={skillset.name} placement="top">
          <Box>
            <AvatarImg
              key={skillset.key}
              shape="SQUARE"
              customSize={20}
              name={skillset.name}
              avatar={skillset.logo || { type: 'PRESET', url: '/assets/ai/skillset/default.png' }}
            />
          </Box>
        </Tooltip>
      ))}

      {/* 当技能集数量超过maxDisplay时，显示"+N"指示器 */}
      {hasMore && (
        <Tooltip title={`+${remainingCount} more skillsets`} placement="top">
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 20,
              height: 20,
              backgroundColor: 'var(--bg-controls)',
              border: '1px solid var(--border-default)',
              borderRadius: '4px',
              fontSize: '10px',
              fontWeight: 500,
              color: 'var(--text-secondary)',
            }}
          >
            +{remainingCount}
          </Box>
        </Tooltip>
      )}
    </Box>
  );
}
