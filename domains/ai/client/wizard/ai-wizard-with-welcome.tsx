import { useLocale } from '@bika/contents/i18n/context';
import type { IAIChatInputStateContext } from '@bika/types/ai/context';
import { snackbarShow } from '@bika/ui/snackbar';
import React, { type MutableRefObject, useCallback, useEffect, useRef } from 'react';
import { AIChatView, type AIChatViewProps, type IAIChatViewHandle } from '../chat/ai-chat-view';
import { AIWelcome, type AIWelcomeProps } from '../chat/ai-welcome';
import { useSendAutoChatState } from './use-send-auto-chat-state';
import { useStartAiChat } from './use-start-chat';

type Props = Pick<
  AIChatViewProps,
  // | 'selector'
  | 'inputState'
  | 'config'
  | 'forceLocale'
  | 'initAIIntent'
  | 'displayMode'
  | 'disabled'
  | 'disabledOperation'
  // | 'context' | 'allowContextMenu'
> &
  Pick<
    AIWelcomeProps,
    | 'title'
    | 'description'
    | 'initPrompts'
    // | 'options'
    | 'customBottom'
    | 'avatars'
  > & {
    // redirect?: () => void;
    fullscreen?: boolean;
    skillsetIcons?: React.ReactNode;
  };

export interface IAIChatWithWelcomeHandle {
  stage: 'welcome' | 'chat';
  startNewChat: () => void;
}
/**
 * AI Wizard Launchpad
 * Just a textbox, before start a wizard
 *
 * @returns
 */
function InternalAIWizardWithWelcome(props: Props, ref: React.Ref<IAIChatWithWelcomeHandle>) {
  const [stage, setStage] = React.useState<'welcome' | 'chat'>('welcome');
  const chatUIHandle: MutableRefObject<IAIChatViewHandle | null> = useRef<IAIChatViewHandle>(null);

  const [chatLoaded, setChatLoaded] = React.useState(false);

  // 避免开发环境 useEffect 执行两次的问题
  const hasInit = React.useRef(false);

  const shouldSkipSendMessage = React.useRef(false);

  const reset = useCallback(() => {
    shouldSkipSendMessage.current = false;
    props.inputState.clear();
    setChatLoaded(false);
    chatUIHandle.current = null;
    setStage('welcome');
  }, [props.inputState]);

  const { data: autoSendInputState, setData: setAutoSendChatInputState } = useSendAutoChatState();

  React.useImperativeHandle(ref, () => ({
    stage,
    startNewChat: () => {
      reset();
    },
  }));

  const send = () => {
    if (!chatUIHandle.current || !chatUIHandle.current.doAppendMessage) {
      return;
    }
    chatUIHandle.current.doAppendMessage(props.inputState.input || '', props.inputState.contexts);
  };

  useEffect(() => {
    if (!chatLoaded) {
      return;
    }
    if (autoSendInputState) {
      props.inputState.patchAIChatInputState({
        contexts: autoSendInputState.contexts ?? [],
        input: autoSendInputState.input ?? '',
      });

      shouldSkipSendMessage.current = true;
      setAutoSendChatInputState(undefined);
      send();
      return;
    }

    if (shouldSkipSendMessage.current) {
      return;
    }
    send();
  }, [chatLoaded, autoSendInputState]);

  const { t } = useLocale();

  const startChat = React.useCallback(() => {
    setStage('chat');
  }, []);

  // 自动设置 Stage 阶段
  React.useEffect(() => {
    if (hasInit.current) {
      return;
    }
    if (autoSendInputState) {
      props.inputState.patchAIChatInputState({
        contexts: autoSendInputState.contexts ?? [],
        input: autoSendInputState.input ?? '',
      });

      setAutoSendChatInputState(undefined);

      setTimeout(() => {
        startChat();
        hasInit.current = true;
        shouldSkipSendMessage.current = false;
      }, 20);
      return;
    }

    if (props.inputState.chatId) {
      // 有强制初始化的 chat？ 马上开始
      setStage('chat');
      // 这种不需要自动发送 chat 自动搞一条
      shouldSkipSendMessage.current = true;
      hasInit.current = true;
    }
  }, [props.inputState, autoSendInputState, startChat]);

  const { startNewChat, isLoading: isCreatingNewChat } = useStartAiChat();

  if (stage === 'welcome') {
    return (
      <AIWelcome
        inputState={props.inputState}
        config={props.config}
        initPrompts={props.initPrompts}
        title={props.title} // t.ai_consultant.title}
        disabled={isCreatingNewChat || props.disabled}
        description={props.description} // t.ai_consultant.description}
        onSubmit={async (_newInputState: IAIChatInputStateContext) => {
          const newChat = await startNewChat(props.initAIIntent);
          snackbarShow({
            content: t.wizard.new_wizard_created,
            color: 'success',
          });

          props.inputState.patchAIChatInputState({
            chatId: newChat.id,
          });

          shouldSkipSendMessage.current = false;
          hasInit.current = true;
          setStage('chat');
        }}
        avatars={props.avatars}
        skillsetIcons={props.skillsetIcons}
        customBottom={props.customBottom}
        fullscreen={props.fullscreen}
      />
    );
  }
  return (
    <AIChatView
      ref={(instance) => {
        if (instance?.doAppendMessage) {
          setChatLoaded(true);
          chatUIHandle.current = instance;
        }
      }}
      inputState={props.inputState}
      config={props.config}
      displayMode={props.displayMode}
      disabled={props.disabled}
      disabledOperation={props.disabledOperation}
      forceLocale={props.forceLocale}
      initAIIntent={props.initAIIntent}
      startNewChat={reset}
      skillsetIcons={props.skillsetIcons}
    />
  );
}

export const AIWizardWithWelcome = React.memo(React.forwardRef(InternalAIWizardWithWelcome));
