import type { AIMessageBO } from '@bika/types/ai/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import { create } from 'zustand';

type Artifact =
  | { message: AIMessageBO; tool: ToolUIPart | DynamicToolUIPart; skillsets: SkillsetSelectDTO[] }
  | undefined;

interface ChatState {
  artifact: Artifact;
  setArtifact: (artifact: Artifact) => void;
}

export const useChatState = create<ChatState>((set) => ({
  artifact: undefined,
  setArtifact: (artifact) => set({ artifact }),
}));
