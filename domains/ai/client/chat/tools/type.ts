import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import type { IToolUIShared } from '../../../../ai-skillset/types';

export type ToolUIProps = IToolUIShared & {
  part: ToolUIPart | DynamicToolUIPart;
  disabled: boolean;

  sendUI: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  onClickTool: (uiPart: ToolUIPart | DynamicToolUIPart) => void;
  onCloseTool?: () => void;
  // skillsets: string[] | undefined;
  error: string | undefined;
};

export const TOOL_RESULT_CANCELED = 'CANCELED';
export const TOOL_RESULT_APPLIED = 'APPLIED';

export interface ToolResultVO {
  error?: {
    message: string;
  };
}
export type ToolResultWithError<T> = ToolResultVO & T;
