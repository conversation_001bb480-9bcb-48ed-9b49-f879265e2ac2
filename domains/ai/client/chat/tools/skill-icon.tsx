import { useTRPCQuery } from '@bika/api-caller/context';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { NodeIcon } from '@bika/ui/node/icon';
import { type DynamicToolUIPart, getToolName, type ToolUIPart } from 'ai';
import React from 'react';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import { CONST_DEFAULT_SKILL_NODE_ICON } from '../../../../ai-skillset/default/client';

interface Props {
  skillsets: SkillsetSelectDTO[];
  toolInvocation: ToolUIPart | DynamicToolUIPart;
}

/**
 *  通过 Skillsets，来真正获得技能图标
 */
function SkillIconBase(props: Props): React.ReactNode {
  const { toolInvocation } = props;
  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );

  const trpcQuery = useTRPCQuery();
  const { data: skillsetsVOs, isLoading: isLoadingSkillsetsVOs } =
    trpcQuery.ai.getSkillsets.useQuery(props.skillsets);

  const toolName =
    toolInvocation.type === 'dynamic-tool' ? toolInvocation.toolName : getToolName(toolInvocation);

  // 从 skillsetsVOs 中获取对应的 skillsetVO, key 和 kind 相同匹配
  const skillsetVO = React.useMemo(() => {
    if (!skillsetsVOs || isLoadingSkillsetsVOs) return undefined;
    const skillset = skillsetsVOs.find((_skillset) => _skillset.key === toolName);
    if (skillset) {
      return skillset;
    }
    return skillsetsVOs.find((_skillset) => _skillset.skills?.some((sk) => sk.key === toolName));
  }, [skillsetsVOs, isLoadingSkillsetsVOs, toolName]);

  let nodeIcon: INodeIconValue | undefined;

  const skillUICfg = skillsetUIMap ? skillsetUIMap[toolName] : undefined;

  if (skillsetVO?.logo) {
    // 从服务端 VO 拿到图标，转换为 INodeIconValue
    nodeIcon = {
      kind: 'avatar' as const,
      avatar: skillsetVO.logo,
      name: skillsetVO.name,
    };
  }

  // 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
  if (skillUICfg?.customIcon) {
    nodeIcon = {
      kind: 'avatar' as const,
      avatar: skillUICfg.customIcon,
    };
  }

  // Use a simple fallback icon if nodeIcon is undefined
  if (!nodeIcon) {
    nodeIcon = CONST_DEFAULT_SKILL_NODE_ICON;
  }

  if (nodeIcon) {
    return <NodeIcon value={nodeIcon} size={32} />;
  }
  return null;
}

export const SkillIcon = React.memo(SkillIconBase);
