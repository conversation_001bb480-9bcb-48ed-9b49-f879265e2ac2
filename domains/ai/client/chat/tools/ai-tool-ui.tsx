'use client';

import { useApi<PERSON>aller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Box } from '@bika/ui/layouts';
import { getToolName } from 'ai';
import { iStringParse } from 'basenext/i18n';
import React from 'react';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import type { ToolUIComponentProps, ToolUIContentProps } from '../../../../ai-skillset/types';
import { useBuildInstallGlobalStore } from '../../../../space/client/intercepter/use-build-install-global-store';
import {
  DefaultToolRenderer,
  getDefaultToolUIContentProps,
  ToolErrorRenderer,
} from './default-tool-renderer';
import { SkillIcon } from './skill-icon';
import type { ToolUIProps } from './type';

export function ToolUI(props: ToolUIProps) {
  const localeContext = useLocale();
  const { setData: setBuildInstallData } = useBuildInstallGlobalStore();
  const spaceContext = useSpaceContextForce();

  const { useRootNode } = spaceContext || { useRootNode: () => ({ setRootNode: () => {} }) };
  const { setRootNode } = useRootNode();

  const { part, hideFlow } = props;

  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );
  const toolName = part.type === 'dynamic-tool' ? part.toolName : getToolName(part);

  const skillUICfg = skillsetUIMap ? skillsetUIMap[toolName] : undefined;

  const handleClickTool = () => {
    props.onClickTool(props.part);
  };

  const defaultToolContentProps: ToolUIContentProps = getDefaultToolUIContentProps(part, {
    displayName: iStringParse(skillUICfg?.displayName, localeContext.lang),
    locale: localeContext,
  });

  const apiCaller = useApiCaller();
  const { trpcQuery } = apiCaller;
  const updateNode = trpcQuery.node.update.useMutation();

  const doExecuteToolResult = async (toolCallId: string) => {
    if (skillUICfg?.clientExecute) {
      // 如果有 clientExecute，说明是一个客户端的 Tool，执行后，可能会有结果更新
      return skillUICfg?.clientExecute?.(part, {
        apiCaller,
        setData: setBuildInstallData,
        setRootNode,
        updateNode: updateNode.mutateAsync,
      });
    }

    // Remote Server trpc Execute Tool
    return props.executeToolResult(toolCallId);
  };

  const renderToolComponentProps: ToolUIComponentProps = {
    executeToolResult: doExecuteToolResult,
    hideFlow,
    isHighlight: props.isHighlight,
    toolInvocation: props.part,
    onClickTool: handleClickTool,
    onCloseTool: props.onCloseTool,
    addToolResult: (userResult) => {
      props.addToolResult({
        tool: props.part.type,
        toolCallId: props.part.toolCallId,
        output: userResult,
      });
    },
    sendMessage: props.sendMessage,
    sendUI: props.sendUI,
    localeContext,
    skillsets: props.skillsets,
  };

  const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part} />;

  if (props.part.state === 'output-error') {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={props.addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={props.skillsets}
          localeContext={localeContext}
          contentProps={{ ...defaultToolContentProps, icon }}
          toolInvocation={props.part}
          error={props.part.errorText}
        />
      </Box>
    );
  }

  if (props.error && defaultToolContentProps) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={props.addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={props.skillsets}
          localeContext={localeContext}
          contentProps={{ ...defaultToolContentProps, icon }}
          toolInvocation={props.part}
          error={props.error}
        />
      </Box>
    );
  }

  if (
    props.part.state === 'output-available' &&
    typeof props.part.output === 'object' &&
    props.part.output &&
    'error' in props.part.output &&
    props.part.output.error
  ) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={props.addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={props.skillsets}
          localeContext={localeContext}
          contentProps={{ ...defaultToolContentProps, icon }}
          toolInvocation={props.part}
          error={(props.part.output.error as { message: string }).message}
        />
      </Box>
    );
  }

  // 出默认，比如第三方 MCP，显示 tool-invocation 的内容
  if (skillUICfg?.component === undefined) {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;
    return (
      <Box>
        <DefaultToolRenderer
          contentProps={{ ...defaultToolContentProps, icon }}
          {...renderToolComponentProps}
        />
      </Box>
    );
  }

  // 定制整个空间，比如，做一个生图 UI Tool
  const componentResult = skillUICfg.component(renderToolComponentProps);
  if (typeof componentResult === 'object') {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;

    //  是否是配置
    const customContentProps = {
      icon,
      ...componentResult,
    } as ToolUIContentProps;
    return (
      <Box>
        <DefaultToolRenderer
          contentProps={{
            ...defaultToolContentProps,
            ...customContentProps,
          }}
          {...renderToolComponentProps}
        />
      </Box>
    );
  }
  return <Box mb={1}>{componentResult}</Box>;
}
