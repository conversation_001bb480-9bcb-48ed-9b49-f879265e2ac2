import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useGlobalContext } from '@bika/types/website/context';
import { IconButton } from '@bika/ui/icon-button';
import ExpandOutlined from '@bika/ui/icons/components/expand_outlined';
import NarrowOutlined from '@bika/ui/icons/components/narrow_outlined';
import { Stack } from '@bika/ui/layouts';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import type React from 'react';
import { useModal } from '../../../../../website/client/context/global/hooks/useModal';
import { useAIArtifactGlobalStore } from '../../../../../website-global-modals/ai-artifact-global-store';
import { DataViewToggle, type DataViewToggleProps, type DataViewVariant } from './data-view-toggle';

export interface ArtifactHeaderProps<T extends object | string> {
  switchProps?: Partial<DataViewToggleProps>;
  expandable?: boolean;
  // placed to thed right
  toolbarButton?: React.ReactElement;
  viewMode?: DataViewVariant;
  isModal?: boolean;

  onExpand?: (isExpaend: boolean) => void;
  skillsets: SkillsetSelectDTO[];
  onViewModeChange: (variant: DataViewVariant) => void;

  title?: string;
  data: T;
  // pass through
  tool?: ToolUIPart | DynamicToolUIPart;
  /**
   * Optional className for custom styling
   */
  className?: string;

  rowDataOnly?: boolean;
}

/**
 * A header component for artifacts that wraps the DataViewToggle component.
 * Provides a consistent header layout with optional title and additional content.
 */
export const ArtifactHeader: React.FC<ArtifactHeaderProps<object | string>> = ({
  className,
  tool,
  switchProps: dataViewToggleProps,
  isModal: propsIsModal,
  skillsets,
  toolbarButton,
  rowDataOnly,
  expandable = true,
  viewMode,
  onExpand,

  onViewModeChange,
}) => {
  const initialMode = rowDataOnly ? 'rowData' : 'preview';
  const checkViewMdoe = viewMode ?? initialMode;
  const modalControl = useModal();

  const globalContext = useGlobalContext();

  const isModal =
    propsIsModal == null ? modalControl.showUIModal?.name === 'AI_ARTIFACT' : propsIsModal;

  const { setData: setGlobalArtifact } = useAIArtifactGlobalStore();

  return (
    <div
      className={`artifact-header ${className || ''}`}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        // padding: '0 16px',
        // backgroundColor: 'var(--bg-surface, #ffffff)',
        minHeight: '32px',
        flex: '0 0 32px',
      }}
    >
      <DataViewToggle
        {...dataViewToggleProps}
        rowDataOnly={rowDataOnly}
        variant={checkViewMdoe}
        onChange={onViewModeChange}
      />

      <Stack alignItems={'center'} direction={'row'}>
        {toolbarButton}

        {!isModal && expandable !== false && (
          <IconButton
            variant="plain"
            size="sm"
            color="neutral"
            onClick={() => {
              if (onExpand) {
                onExpand(true);
                return;
              }
              if (tool) {
                setGlobalArtifact({
                  message: { id: '', role: 'assistant', parts: [] },
                  tool,
                  skillsets,
                });
                globalContext.showUIModal({ name: 'AI_ARTIFACT' });
              }
            }}
            sx={{
              '&:hover': {
                backgroundColor: 'var(--hover)',
              },
            }}
          >
            <ExpandOutlined color="text-primary" />
          </IconButton>
        )}

        {isModal && (
          <IconButton
            variant="plain"
            size="sm"
            color="neutral"
            onClick={() => {
              if (onExpand) {
                onExpand(false);
                return;
              }
              globalContext.showUIModal(null);
            }}
            sx={{
              '&:hover': {
                backgroundColor: 'var(--hover)',
              },
            }}
          >
            <NarrowOutlined color="text-primary" />
          </IconButton>
        )}
      </Stack>
    </div>
  );
};
