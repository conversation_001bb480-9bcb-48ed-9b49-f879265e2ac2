import { useLocale } from '@bika/contents/i18n';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/icon-button';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import { Box } from '@bika/ui/layouts';
import { styled } from '@bika/ui/styled';
import { Typography } from '@bika/ui/texts';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import { useRef } from 'react';
import type { TableViewRendererRefHandle } from '../../../../database/client/table-view/table-view-renderer';
import { ArtifactContainerWithModal } from './components/artifact-container-with-modal';
import { type GridDataProps, GridViewComponent } from './grid-view-component';

interface GridArtifactProps {
  data: GridDataProps;
  tool?: ToolUIPart | DynamicToolUIPart;
  isModal?: boolean;
  skillsets?: SkillsetSelectDTO[];
}

const StyledGridViewComponent = styled(GridViewComponent)`
  height: 100% !important;
  width: 100% !important;
`;

export const GridArtifact = (props: GridArtifactProps) => {
  const { t } = useLocale();
  const { tool, skillsets = [] } = props;
  const showLoadingState = props.tool?.state === 'input-streaming';

  const ref = useRef<TableViewRendererRefHandle>(null);

  // Show loading state when configured to display loading placeholder
  if (showLoadingState) {
    return (
      <Typography textColor="var(--text-secondary)" level="b4">
        {t.pagination.loading}
      </Typography>
    );
  }

  // Create the download button component
  const downloadButton = (
    <>
      <span className="w-0 h-0 opacity-0 hidden">
        <StyledGridViewComponent ref={ref} data={props.data} />
      </span>
      <IconButton
        variant="plain"
        size="sm"
        color="neutral"
        onClick={() => {
          console.log('re', ref);
          ref.current?.getApi?.()?.exportDataAsExcel({
            fileName: 'datasheet.xlsx',
          });
        }}
        sx={{
          '&:hover': {
            backgroundColor: 'var(--hover)',
          },
        }}
      >
        <DownloadOutlined color="var(--text-primary)" />
      </IconButton>
    </>
  );

  return (
    <ArtifactContainerWithModal
      data={props.data}
      skillsets={skillsets}
      tool={tool}
      switchProps={{
        rowDataLabel: t.ai.artifact_code,
        previewLabel: t.ai.artifact_preview,
      }}
      toolbarButton={downloadButton}
    >
      <Box
        sx={{
          height: '40px',
          minHeight: '40px',
          width: '100%',
          backgroundColor: 'var(--bg-controls)',
          justifyContent: 'center',
          alignItems: 'center',
          display: 'flex',
          marginBottom: '16px',
        }}
      >
        <Typography textColor="var(--text-primary)" level="b4">
          {t.data.database}
        </Typography>
      </Box>
      <StyledGridViewComponent
        data={props.data}
        className="!w-full !h-full flex-auto px-[24px] bg-[var(--bg-controls)]"
      />
    </ArtifactContainerWithModal>
  );
};
