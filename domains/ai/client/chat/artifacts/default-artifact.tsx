import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { DynamicToolUIPart, ToolUIPart } from 'ai';
import React from 'react';
import { ArtifactContainer } from './components/artifact-container';

interface DefaultArtifactProps {
  resources: object;
  skillsets: SkillsetSelectDTO[];
  tool: ToolUIPart | DynamicToolUIPart;
}

export const DefaultArtifact = (props: DefaultArtifactProps) => {
  const { resources, skillsets, tool } = props;
  return (
    <ArtifactContainer
      data={resources}
      skillsets={skillsets}
      rowDataOnly={true}
      tool={tool}
      switchProps={{}}
      toolbarButton={<></>}
    />
  );
};
