import { useLocale } from '@bika/contents/i18n/context';
import type { AIMessageBO } from '@bika/types/ai/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/button';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { Typography } from '@bika/ui/texts';
import type { SxProps } from '@mui/joy/styles/types';
import { type DynamicToolUIPart, getToolName, type ToolUIPart } from 'ai';
import { get } from 'lodash';
import React from 'react';
import { AIArtifactServer } from '../../../../ai-artifacts/ai-server-artifact-ui';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import { CONST_DEFAULT_SKILL_NODE_ICON } from '../../../../ai-skillset/default/client';
import { SkillIcon } from '../tools/skill-icon';
import type { ToolResultWithError } from '../tools/type';
import { AIArtifactByAIUI } from './ai-artifact-ai-ui';
import { DefaultArtifact } from './default-artifact';

export interface IArtifactProps {
  message: AIMessageBO;
  skillsets: SkillsetSelectDTO[];
  tool: ToolUIPart | DynamicToolUIPart;
  isModal?: boolean;
  onClickClose: () => void;
  sx?: SxProps;
  isCopilot?: boolean;
}

interface WindowProps {
  onClickClose: () => void;
  children: React.ReactNode;
  toolConfig?: {
    roleName: string;
    subRoleName?: string;
    roleState?: string;
  };
  icon?: React.ReactNode;
  sx?: SxProps;
}

function Window(props: WindowProps) {
  const { roleName, subRoleName } = props.toolConfig || {};
  return (
    <Stack
      direction={'column'}
      sx={{
        border: '1px solid var(--border-default)',
        width: 400,
        height: 'calc(100% - 32px)',
        borderRadius: '8px',
        background: 'var(--bg-surface)',
        minWidth: '374px',
        boxShadow: 'var(--shadow-high)',
        mb: 1,
        ...props.sx,
      }}
    >
      <Box
        px={3}
        py={2}
        justifyContent="space-between"
        alignItems="center"
        display="flex"
        borderBottom="1px solid var(--border-default)"
      >
        {props.toolConfig && (
          <Stack direction="row" gap={2} alignItems="center">
            {props.icon || <NodeIcon value={CONST_DEFAULT_SKILL_NODE_ICON} />}
            <Box>
              <Typography level="b2" textColor="var(--text-primary)">
                {roleName}
              </Typography>
              {subRoleName && (
                <Typography level="b4" textColor="var(--text-secondary)">
                  {subRoleName}
                </Typography>
              )}
            </Box>
          </Stack>
        )}

        <IconButton
          onClick={() => {
            props.onClickClose();
          }}
        >
          <CloseOutlined color="var(--text-primary)" />
        </IconButton>
      </Box>

      <Box display="flex" px={3} py={2} flex={1} sx={{ overflow: 'auto' }}>
        {props.children}
      </Box>
    </Stack>
  );
}

function AIChatArtifactBase(props: IArtifactProps) {
  const { tool } = props;
  const localeContext = useLocale();
  const { t } = localeContext;

  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );

  const toolName = tool.type === 'dynamic-tool' ? tool.toolName : getToolName(tool);

  const toolUIConfig = skillsetUIMap[toolName];
  console.log('🚀 ~ AIChatArtifactBase ~ toolUIConfig:', toolUIConfig);

  const toolConfig = {
    roleName: toolName,
    subRoleName: tool.state === 'output-available' ? t.ai.completed : t.ai.in_progress,
  };

  const toolIcon = <SkillIcon skillsets={props.skillsets} toolInvocation={tool} />;

  const Wrapper = props.isCopilot
    ? Box
    : (windowProps: Omit<WindowProps, 'toolConfig'>) => (
        <Window {...windowProps} toolConfig={toolConfig} icon={toolIcon} />
      );

  const result = get(tool, 'result') as ToolResultWithError<unknown> | undefined;

  if (result?.error) {
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <Typography level="b4" textColor="'var(--status-danger)">
          {result.error.message}
        </Typography>
      </Wrapper>
    );
  }

  if (!toolUIConfig || toolUIConfig.artifact === undefined) {
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <DefaultArtifact resources={tool || {}} tool={tool} skillsets={props.skillsets} />
      </Wrapper>
    );
  }

  if (typeof toolUIConfig.artifact === 'string') {
    if (toolUIConfig.artifact === 'ai-ui') {
      return (
        <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
          <AIArtifactByAIUI {...props} />
        </Wrapper>
      );
    }
    if (toolUIConfig.artifact === 'server-artifact') {
      return (
        <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
          <AIArtifactServer {...props} />
        </Wrapper>
      );
    }
  } else {
    // UI Component
    return (
      <Wrapper onClickClose={props.onClickClose} sx={props.sx}>
        <toolUIConfig.artifact
          toolInvocation={tool}
          localeContext={localeContext}
          skillsets={props.skillsets}
        />
      </Wrapper>
    );
  }
}

export const AIChatArtifact = React.memo(AIChatArtifactBase, (prevProps, nextProps) => {
  if (prevProps.tool.toolCallId !== nextProps.tool.toolCallId) {
    return false;
  }
  if (prevProps.isCopilot !== nextProps.isCopilot) {
    return false;
  }
  if (prevProps.tool !== nextProps.tool) {
    return false;
  }
  return true;
});
