{"name": "toolsdk", "version": "2.0.0-beta.7", "description": "Toolsdk.ai's SDK, includes API, Developer, React UI components and Types", "scripts": {"publish": "npm publish --access public"}, "exports": {"./api": {"types": "./dist/api/index.d.ts", "import": "./dist/api/index.esm.js", "require": "./dist/api/index.cjs.js"}, "./developer": {"types": "./dist/developer/index.d.ts", "import": "./dist/developer/index.esm.js", "require": "./dist/developer/index.cjs.js"}, "./react": {"types": "./dist/react/index.d.ts", "import": "./dist/react/index.esm.js", "require": "./dist/react/index.cjs.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.esm.js", "require": "./dist/types/index.cjs.js"}}, "keywords": [], "author": "toolsdk.ai", "license": "ISC"}