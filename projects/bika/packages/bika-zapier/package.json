{"name": "@bika.ai/bika-zapier", "version": "2.0.0-beta.7", "description": "Bika.ai's Zapier integration", "main": "index.js", "scripts": {"build": "node ./build.mjs", "push": "zapier push", "validate": "npm run build && zapier validate"}, "dependencies": {"axios": "^1.10.0", "zapier-platform-core": "15.18.1"}, "devDependencies": {"@bika/types": "file:../bika-types", "@types/bun": "latest", "@types/jest": "^29.4.0", "@types/node": "^20.11.24", "basenext": "file:../../../basenext", "bika.ai": "file:../bika-sdk-js", "jest": "^29.4.3", "nock": "^13.3.0", "prettier": "^3.4.2", "rimraf": "^4.1.2", "ts-jest": "^29.0.5", "typescript": "^5.8.3"}, "private": true, "jest": {"verbose": true, "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "./src", "testRegex": [".test.ts$", ".test.js$"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "transformIgnorePatterns": ["^.+\\.js$"], "moduleDirectories": ["node_modules", "src"], "testEnvironment": "node"}}