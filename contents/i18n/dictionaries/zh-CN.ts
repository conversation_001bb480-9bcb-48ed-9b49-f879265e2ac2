const dict = {
  about: {
    about: '关于',
    address: '488 The Bridle Walk, Toronto, ON L6C 2Y4 Canada',
    cn_license: '备案号：粤ICP备********号-4 公安备案号：**************',
    copyright: '版权所有 © 2025 Bika.ai',
    license: '用户协议',
    permission: '应用权限说明',
    privacy_policy: '隐私政策',
    rate_us: '给我们评价',
    release_notes: '更新日志',
    safety: '安全白皮书',
  },
  account: {
    account_binding: '账号绑定',
    account_binding_and_security: '账号绑定与安全',
    account_binding_description:
      '为了更好地接收提醒和处理任务,请选择以下一种方式登录您的 Bika 账户。',
    account_binding_error: '账号绑定错误',
    account_binding_error_description: '绑定账号时出现错误，请稍后再试。',
    account_binding_error_type: '当前{type}已绑定过其他账号',
    account_binding_success: '账号绑定成功',
    account_info: '账户信息',
    advanced_features: '高级功能',
    bind_now: '立即绑定',
    bound: '已绑定',
    delete_account: '删除账户',
    delete_account_description: '删除账户后将无法恢复，确定要删除账户吗?',
    destroy_account: '注销账户',
    social_account_binding: '社交账号绑定',
    social_account_binding_description: '绑定社交账号以解锁更多功能',
    subscribe_now: '立即订阅',
    use_advanced_features: '使用高级功能',
    use_advanced_features_description: '打开高级功能以提高团队效率',
  },
  action: {
    accept: '接受',
    accepted: '已接受',
    add: '添加',
    added: '已添加',
    adding: '添加中...',
    again: '再试一次',
    apply: '批准',
    auth: '认证',
    back: '返回',
    cancel: '取消',
    choose: '选择',
    click_here: '点击这里',
    close: '关闭',
    coming_soon: '即将推出',
    coming_soon_description: '此功能即将推出',
    comment: '评论',
    commented: '已评论',
    complete: '完成',
    completed: '已完成',
    confirm: '确认',
    connect: '连接',
    connected: '已连接',
    contact_community: '联系社区',
    create: '创建',
    create_data: '新增数据',
    create_folder: '创建文件夹',
    create_resource: '创建资源',
    created: '已创建',
    current: '当前',
    decline: '拒绝',
    declined: '已拒绝',
    delete: '删除',
    delete_resource: '删除资源',
    deleted: '已删除',
    detail: '详情',
    due: '逾期',
    duplicate: '复制',
    edit: '编辑',
    edit_resource: '编辑资源',
    edited: '已编辑',
    email_sent: '邮件已发送',
    failed: '失败',
    get: '获取',
    import: '导入',
    install: '安装',
    installed: '已安装',
    loading: '加载中...',
    manual_complete: '手动完成',
    more: '了解更多',
    move: '移动',
    next: '下一步',
    no: '否',
    not_supported: '暂不支持',
    not_supported_description: '此功能暂不支持',
    ok: '确定',
    only_on_web: '仅支持 Web 版',
    only_on_web_description: '此功能仅支持 Web 版',
    only_on_web_editor: '编辑器',
    only_on_web_editor_description: '此功能仅支持 Web 版编辑器',
    overdue: '已过期',
    process: '处理',
    processing: '处理中',
    reauthorize_integration: '重新授权',
    redirect: '跳转',
    remove: '移除',
    removed: '已移除',
    rename: '重命名',
    save: '保存',
    saved: '已保存',
    search: '搜索',
    search_placeholder: '搜索...',
    search_result: '搜索结果',
    search_result_description: '搜索结果',
    search_result_not_found: '未找到 "{keyword}" 的结果',
    search_result_not_found_description: '未找到 "{keyword}" 的结果',
    searching: '搜索中...',
    select: '选择',
    select_all: '全选',
    selected: '已选择',
    send: '发送',
    submit: '提交',
    submitted: '已提交',
    submitter: '提交人',
    transfer: '转移',
    transferred: '已转移',
    uninstall: '卸载',
    uninstalled: '已卸载',
    unselect_all: '取消全选',
    view: '查看',
    viewed: '已查看',
    yes: '是',
  },
  ag_grid: {
    AreaColumnCombo: '面积图和柱状图组合',
    addCurrentSelectionToFilter: '将当前选择添加到筛选器',
    addToLabels: '将${variable}添加到标签',
    addToValues: '将${variable}添加到值',
    advancedFilterAnd: '且',
    advancedFilterApply: '应用',
    advancedFilterBlank: '为空',
    advancedFilterBuilder: '构建器',
    advancedFilterBuilderAddButtonTooltip: '添加筛选或组',
    advancedFilterBuilderAddCondition: '添加筛选',
    advancedFilterBuilderAddJoin: '添加组',
    advancedFilterBuilderApply: '应用',
    advancedFilterBuilderCancel: '取消',
    advancedFilterBuilderEnterValue: '输入一个值...',
    advancedFilterBuilderMoveDownButtonTooltip: '下移',
    advancedFilterBuilderMoveUpButtonTooltip: '上移',
    advancedFilterBuilderRemoveButtonTooltip: '移除',
    advancedFilterBuilderSelectColumn: '选择一个列',
    advancedFilterBuilderSelectOption: '选择一个选项',
    advancedFilterBuilderTitle: '高级筛选',
    advancedFilterBuilderValidationAlreadyApplied: '当前筛选已应用。',
    advancedFilterBuilderValidationEnterValue: '必须输入一个值。',
    advancedFilterBuilderValidationIncomplete: '并非所有条件都已完成。',
    advancedFilterBuilderValidationSelectColumn: '必须选择一个列。',
    advancedFilterBuilderValidationSelectOption: '必须选择一个选项。',
    advancedFilterContains: '包含',
    advancedFilterEndsWith: '结束于',
    advancedFilterEquals: '=',
    advancedFilterFalse: '为假',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterNotBlank: '不为空',
    advancedFilterNotContains: '不包含',
    advancedFilterNotEqual: '!=',
    advancedFilterOr: '或',
    advancedFilterStartsWith: '开始于',
    advancedFilterTextEquals: '等于',
    advancedFilterTextNotEqual: '不等于',
    advancedFilterTrue: '为真',
    advancedFilterValidationExtraEndBracket: '结束括号过多',
    advancedFilterValidationInvalidColumn: '找不到列',
    advancedFilterValidationInvalidDate: '值不是一个有效日期',
    advancedFilterValidationInvalidJoinOperator: '找不到连接操作符',
    advancedFilterValidationInvalidOption: '找不到选项',
    advancedFilterValidationJoinOperatorMismatch: '一个条件内的连接操作符必须相同',
    advancedFilterValidationMessage: '表达式有错误。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '表达式有错误。表达式末尾的${variable}。',
    advancedFilterValidationMissingColumn: '缺少列',
    advancedFilterValidationMissingCondition: '缺少条件',
    advancedFilterValidationMissingEndBracket: '缺少结束括号',
    advancedFilterValidationMissingOption: '缺少选项',
    advancedFilterValidationMissingQuote: '值缺少结束引号',
    advancedFilterValidationMissingValue: '缺少值',
    advancedFilterValidationNotANumber: '值不是一个数字',
    advancedSettings: '高级设置',
    after: '之后',
    aggregate: '汇总',
    andCondition: '和',
    animation: '动画',
    applyFilter: '应用',
    april: '四月',
    area: '面积',
    areaChart: '面积图',
    areaColumnComboTooltip: '面积图与柱图',
    areaGroup: '面积图',
    ariaAdvancedFilterBuilderColumn: '列',
    ariaAdvancedFilterBuilderFilterItem: '过滤条件',
    ariaAdvancedFilterBuilderGroupItem: '过滤组',
    ariaAdvancedFilterBuilderItem: '${variable}. 级别 ${variable}. 按 ENTER 进行编辑。',
    ariaAdvancedFilterBuilderItemValidation:
      '${variable}. 级别 ${variable}. ${variable} 按 ENTER 进行编辑。',
    ariaAdvancedFilterBuilderJoinOperator: '连接运算符',
    ariaAdvancedFilterBuilderList: '高级过滤器构建器列表',
    ariaAdvancedFilterBuilderOption: '选项',
    ariaAdvancedFilterBuilderValueP: '值',
    ariaAdvancedFilterInput: '高级过滤器输入',
    ariaChartMenuClose: '关闭图表编辑菜单',
    ariaChartSelected: '已选择',
    ariaChecked: '已选中',
    ariaColumn: '列',
    ariaColumnFiltered: '列已过滤',
    ariaColumnGroup: '列组',
    ariaColumnPanelList: '列列表',
    ariaColumnSelectAll: '切换选择所有列',
    ariaDateFilterInput: '日期过滤器输入',
    ariaDefaultListName: '列表',
    ariaDropZoneColumnComponentAggFuncSeparator: ' 的 ',
    ariaDropZoneColumnComponentDescription: '按 DELETE 键移除',
    ariaDropZoneColumnComponentSortAscending: '升序',
    ariaDropZoneColumnComponentSortDescending: '降序',
    ariaDropZoneColumnGroupItemDescription: '按 ENTER 键排序',
    ariaDropZoneColumnValueItemDescription: '按 ENTER 键更改聚合类型',
    ariaFilterColumn: '按 CTRL ENTER 打开过滤器',
    ariaFilterColumnsInput: '过滤列输入',
    ariaFilterFromValue: '过滤从值',
    ariaFilterInput: '过滤器输入',
    ariaFilterList: '过滤器列表',
    ariaFilterMenuOpen: '打开过滤器菜单',
    ariaFilterPanelList: '过滤列表',
    ariaFilterToValue: '过滤至值',
    ariaFilterValue: '过滤值',
    ariaFilteringOperator: '过滤运算符',
    ariaHidden: '隐藏',
    ariaIndeterminate: '不确定',
    ariaInputEditor: '输入编辑器',
    ariaLabelAdvancedFilterAutocomplete: '高级筛选自动完成',
    ariaLabelAdvancedFilterBuilderAddField: '高级筛选生成器添加字段',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高级筛选生成器列选择字段',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高级筛选生成器连接操作符选择字段',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高级筛选生成器选项选择字段',
    ariaLabelAggregationFunction: '聚合函数',
    ariaLabelCellEditor: '单元格编辑器',
    ariaLabelColumnFilter: '列过滤器',
    ariaLabelColumnMenu: '列菜单',
    ariaLabelContextMenu: '上下文菜单',
    ariaLabelDialog: '对话框',
    ariaLabelRichSelectDeleteSelection: '按下删除键来取消选择项目',
    ariaLabelRichSelectDeselectAllItems: '按下删除键来取消选择所有项目',
    ariaLabelRichSelectField: '丰富选择字段',
    ariaLabelRichSelectToggleSelection: '按下空格键以切换选择',
    ariaLabelSelectField: '选择字段',
    ariaLabelSubMenu: '子菜单',
    ariaLabelTooltip: '工具提示',
    ariaMenuColumn: '按 ALT 向下 打开列菜单',
    ariaPageSizeSelectorLabel: '页面大小',
    ariaPivotDropZonePanelLabel: '列标签',
    ariaRowDeselect: '按 SPACE 取消选择此行',
    ariaRowGroupDropZonePanelLabel: '行分组',
    ariaRowSelect: '按 SPACE 选择此行',
    ariaRowSelectAll: '按 Space 切换所有行选择',
    ariaRowSelectionDisabled: '此行的行选择功能被禁用',
    ariaRowToggleSelection: '按 Space 切换行选择',
    ariaSearch: '搜索',
    ariaSearchFilterValues: '搜索过滤值',
    ariaSkeletonCellLoading: '行数据加载中',
    ariaSkeletonCellLoadingFailed: '行加载失败',
    ariaSortableColumn: '按 ENTER 排序',
    ariaToggleCellValue: '按 Space 切换单元格值',
    ariaToggleVisibility: '按 Space 切换可见性',
    ariaUnchecked: '未选中',
    ariaValuesDropZonePanelLabel: '值',
    ariaVisible: '可见',
    asc_option: '选项正序',
    august: '八月',
    autoRotate: '自动旋转',
    automatic: '自动',
    autosizeAllColumns: '自动调整所有列',
    autosizeThisColumn: '自动调整该列',
    avg: '平均',
    axis: '轴',
    axisType: '轴类型',
    background: '背景',
    bar: '条形图',
    barChart: '条形图',
    barGroup: '条形图',
    before: '之前',
    blank: '空白',
    blanks: '(空白)',
    blur: '模糊',
    bold: '加粗',
    boldItalic: '加粗斜体',
    bottom: '底部',
    boxPlot: '箱线图',
    boxPlotTooltip: '箱线图',
    bubble: '气泡图',
    bubbleTooltip: '气泡图',
    callout: '标注',
    calloutLabels: '标注标签',
    cancelFilter: '取消',
    cap: '顶部',
    capLengthRatio: '顶部长度比',
    categories: '类别',
    category: '类别',
    categoryAdd: '添加类别',
    categoryValues: '类别值',
    chartAdvancedSettings: '高级设置',
    chartDownload: '下载图表',
    chartDownloadToolbarTooltip: '下载图表',
    chartEdit: '编辑图表',
    chartLink: '链接到网格',
    chartLinkToolbarTooltip: '链接到网格',
    chartMenuToolbarTooltip: '菜单',
    chartRange: '图表范围',
    chartSettingsToolbarTooltip: '菜单',
    chartStyle: '图表样式',
    chartSubtitle: '副标题',
    chartTitle: '图表标题',
    chartTitles: '标题',
    chartUnlink: '从网格中取消链接',
    chartUnlinkToolbarTooltip: '从网格中取消链接',
    circle: '圆形',
    clearFilter: '清除',
    collapseAll: '关闭所有行组',
    color: '颜色',
    column: '柱形图',
    columnChart: '柱状图',
    columnChooser: '选择列',
    columnFilter: '列过滤',
    columnGroup: '柱形图',
    columnLineCombo: '柱状图和折线图组合',
    columnLineComboTooltip: '柱图与折线图',
    columns: '列',
    combinationChart: '组合图',
    combinationGroup: '组合图',
    connectorLine: '连接线',
    contains: '包含',
    copy: '复制',
    copyWithGroupHeaders: '复制包含组标题',
    copyWithHeaders: '复制包含标题',
    copy_row: '复制行',
    count: '计数',
    cross: '十字符',
    crosshair: '准星',
    crosshairLabel: '标签',
    crosshairSnap: '对节点对齐',
    csvExport: '导出为CSV',
    ctrlC: 'Ctrl+C',
    ctrlV: 'Ctrl+V',
    ctrlX: 'Ctrl+X',
    customCombo: '自定义组合',
    customComboTooltip: '自定义组合',
    cut: '剪切',
    data: '设置',
    dateFilter: '日期过滤器',
    dateFormatOoo: 'yyyy-mm-dd',
    december: '十二月',
    decimalSeparator: '.',
    defaultCategory: '(无)',
    desc_option: '选项倒序',
    diamond: '菱形',
    direction: '方向',
    donut: '环形图',
    donutTooltip: '环形图',
    duplicate_record: '重复记录',
    durationMillis: '持续时间 (毫秒)',
    empty: '选择一个',
    enabled: '启用',
    endAngle: '终止角度',
    endsWith: '结束于',
    equals: '等于',
    excelExport: '导出为Excel',
    expandAll: '展开所有行组',
    expand_record: '展开记录',
    export: '导出',
    false: '假',
    february: '二月',
    fillOpacity: '填充不透明度',
    filterOoo: '过滤...',
    filteredRows: '已筛选',
    filters: '过滤器',
    first: '第一个',
    firstPage: '第一页',
    fixed: '固定',
    font: '字体',
    footerTotal: '合计',
    format: '自定义',
    greaterThan: '大于',
    greaterThanOrEqual: '大于等于',
    gridLines: '网格线',
    group: '组',
    groupBy: '按此分组',
    groupFilterSelect: '选择字段:',
    groupPadding: '组间距',
    groupedAreaTooltip: '面积图',
    groupedBar: '分组',
    groupedBarFull: '分组条形图',
    groupedBarTooltip: '分组',
    groupedColumn: '分组',
    groupedColumnFull: '分组柱形图',
    groupedColumnTooltip: '分组',
    groupedSeriesGroupType: '分组',
    groups: '行组',
    heart: '爱心',
    heatmap: '热力图',
    heatmapTooltip: '热力图',
    height: '高度',
    hierarchicalChart: '层次图',
    hierarchicalGroup: '层次图',
    histogram: '直方图',
    histogramBinCount: '箱数',
    histogramChart: '直方图',
    histogramFrequency: '频率',
    histogramTooltip: '直方图',
    horizontal: '水平',
    horizontalAxisTitle: '水平轴标题',
    inRange: '介于',
    inRangeEnd: '到',
    inRangeStart: '从',
    innerRadius: '内半径',
    inside: '内部',
    invalidColor: '无效的颜色值',
    invalidDate: '无效日期',
    invalidNumber: '无效数字',
    italic: '斜体',
    itemPaddingX: '项目内边距 X',
    itemPaddingY: '项目内边距 Y',
    itemSpacing: '项目间距',
    january: '一月',
    july: '七月',
    june: '六月',
    labelPlacement: '标签位置',
    labelRotation: '旋转',
    labels: '标签',
    last: '最后一个',
    lastPage: '最后一页',
    layoutHorizontalSpacing: '横向间距',
    layoutVerticalSpacing: '纵向间距',
    left: '左边',
    legend: '图例',
    legendEnabled: '启用',
    length: '长度',
    lessThan: '小于',
    lessThanOrEqual: '小于等于',
    line: '折线图',
    lineDash: '线条虚线',
    lineDashOffset: '虚线偏移',
    lineGroup: '折线图',
    lineTooltip: '折线图',
    lineWidth: '线宽',
    loadingError: '错误',
    loadingOoo: '加载中...',
    lookup_unamed_record: '未命名记录',
    march: '三月',
    markerPadding: '标记内边距',
    markerSize: '标记大小',
    markerStroke: '标记描边',
    markers: '标记',
    max: '最大值',
    maxSize: '最大大小',
    may: '五月',
    min: '最小值',
    minSize: '最小大小',
    miniChart: '迷你图表',
    more: '更多',
    navigator: '导航器',
    nextPage: '下一页',
    nightingale: '夜莺图',
    nightingaleTooltip: '玫瑰图',
    noAggregation: '无',
    noDataToChart: '无可绘制的数据。',
    noMatches: '无匹配项',
    noPin: '取消固定',
    noRowsToShow: '无显示行',
    none: '无',
    normal: '常规',
    normalizedArea: '100% 堆积',
    normalizedAreaFull: '100% 堆积面积图',
    normalizedAreaTooltip: '100% 堆积',
    normalizedBar: '100% 堆积',
    normalizedBarFull: '100% 堆积条形图',
    normalizedBarTooltip: '100% 堆积',
    normalizedColumn: '100% 堆积',
    normalizedColumnFull: '100% 堆积柱形图',
    normalizedColumnTooltip: '100% 堆积',
    normalizedSeriesGroupType: '100% 堆积',
    notBlank: '非空',
    notContains: '不包含',
    notEqual: '不等于',
    november: '十一月',
    number: '数值',
    numberFilter: '数字过滤器',
    october: '十月',
    of: '的',
    offset: '偏移',
    offsets: '偏移',
    orCondition: '或',
    orientation: '方向',
    outside: '外部',
    padding: '内边距',
    page: '页',
    pageLastRowUnknown: '?',
    pageSizeSelectorLabel: '每页大小：',
    paired: '配对模式',
    parallel: '平行',
    paste: '粘贴',
    pasting_multiple_columns_is_not_supportted_currently: '暂不支持粘贴多列数据',
    perpendicular: '垂直',
    pie: '饼图',
    pieChart: '饼图',
    pieGroup: '饼图',
    pieTooltip: '饼图',
    pinColumn: '固定列',
    pinLeft: '固定在左侧',
    pinRight: '固定在右侧',
    pivotChart: '数据透视图',
    pivotChartAndPivotMode: '数据透视图和数据透视模式',
    pivotChartRequiresPivotMode: '数据透视图需要启用数据透视模式。',
    pivotChartTitle: '数据透视图',
    pivotColumnGroupTotals: '总计',
    pivotColumnsEmptyMessage: '拖动到此处设置列标签',
    pivotMode: '透视模式',
    pivots: '列标签',
    plus: '加号',
    polarAxis: '极坐标轴',
    polarAxisTitle: '极坐标轴标题',
    polarChart: '极地图',
    polarGroup: '极坐标图',
    polygon: '多边形',
    position: '位置',
    positionRatio: '位置比例',
    predefined: '预定义',
    preferredLength: '首选长度',
    previousPage: '上一页',
    radarArea: '雷达面积',
    radarAreaTooltip: '雷达面积图',
    radarLine: '雷达线',
    radarLineTooltip: '雷达线图',
    radialBar: '径向条形图',
    radialBarTooltip: '径向条图',
    radialColumn: '径向柱状图',
    radialColumnTooltip: '径向柱图',
    radiusAxis: '半径轴',
    radiusAxisPosition: '位置',
    rangeArea: '区间面积图',
    rangeAreaTooltip: '范围面积图',
    rangeBar: '区间条形图',
    rangeBarTooltip: '范围条图',
    rangeChartTitle: '范围图',
    removeFromLabels: '将${variable}从标签中移除',
    removeFromValues: '将${variable}从值中移除',
    resetColumns: '重置列',
    resetFilter: '重置',
    reverseDirection: '反向',
    right: '右边',
    rowDragRow: '行',
    rowDragRows: '行',
    rowGroupColumnsEmptyMessage: '拖动到此处设置行组',
    row_group: '分组',
    scatter: '散点图',
    scatterGroup: '散点图',
    scatterTooltip: '散点图',
    scrollingStep: '滚动步骤',
    scrollingZoom: '滚动',
    searchOoo: '搜索...',
    secondaryAxis: '次轴',
    sectorLabels: '扇区标签',
    see_more_detail: '查看更多',
    selectAll: '(全选)',
    selectAllSearchResults: '(全选搜索结果)',
    selectedRows: '已选中',
    selectingZoom: '选择',
    september: '九月',
    series: '系列',
    seriesAdd: '添加系列',
    seriesChartType: '系列图表类型',
    seriesGroupType: '分组类型',
    seriesItemLabels: '项目标签',
    seriesItemNegative: '负面',
    seriesItemPositive: '正面',
    seriesItemType: '项目类型',
    seriesItems: '系列项目',
    seriesLabels: '系列标签',
    seriesPadding: '系列间距',
    seriesType: '系列类型',
    setFilter: '集合过滤器',
    settings: '图表',
    shadow: '阴影',
    shape: '形状',
    size: '大小',
    sortAscending: '升序排列',
    sortDescending: '降序排列',
    sortUnSort: '清除排序',
    spacing: '间距',
    specializedChart: '专项图',
    specializedGroup: '专用图',
    square: '方形',
    stackedArea: '堆积',
    stackedAreaFull: '堆积面积图',
    stackedAreaTooltip: '堆积',
    stackedBar: '堆积',
    stackedBarFull: '堆积条形图',
    stackedBarTooltip: '堆积',
    stackedColumn: '堆积',
    stackedColumnFull: '堆积柱形图',
    stackedColumnTooltip: '堆积',
    stackedSeriesGroupType: '堆积',
    startAngle: '起始角度',
    startsWith: '开始于',
    statisticalChart: '统计图',
    statisticalGroup: '统计图',
    strokeColor: '线条颜色',
    strokeOpacity: '线条不透明度',
    strokeWidth: '描边宽度',
    sum: '总和',
    sunburst: '旭日图',
    sunburstTooltip: '旭日图',
    switchCategorySeries: '切换类别 / 系列',
    textFilter: '文本过滤器',
    thickness: '厚度',
    thousandSeparator: ',',
    ticks: '刻度',
    tile: '瓦片',
    time: '时间',
    timeFormat: '时间格式',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS 上午/下午',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    title: '标题',
    titlePlaceholder: '图表标题',
    to: '至',
    tooltips: '工具提示',
    top: '顶部',
    totalAndFilteredRows: '行',
    totalRows: '总行数',
    treemap: '树图',
    treemapTooltip: '树状图',
    triangle: '三角形',
    true: '真',
    ungroupAll: '取消全部分组',
    ungroupBy: '取消按此分组',
    valueAggregation: '值汇总',
    valueColumnsEmptyMessage: '拖动到此处聚合',
    values: '值',
    vertical: '垂直',
    verticalAxisTitle: '垂直轴标题',
    waterfall: '瀑布图',
    waterfallTooltip: '瀑布图',
    weight: '粗细',
    whisker: '须',
    width: '宽度',
    xAxis: '水平轴',
    xOffset: 'X 偏移',
    xType: 'X 类型',
    xyChart: 'X Y (散点图)',
    xyValues: 'XY 值',
    yAxis: '垂直轴',
    yOffset: 'Y 偏移',
    zoom: '缩放',
  },
  agenda: {
    agenda: '议程',
    description: '描述',
    reminder_time: '提醒时间',
  },
  ai: {
    ai_image_generation: 'AI 图像生成',
    ai_models_feature: 'AI Models',
    ai_models_feature_description:
      'Bika.ai comes with multiple preset AI models, including the latest offerings from OpenAI, Anthropic, and more, as well as integrated text-to-text, text-to-image, and text-to-video models.\n\nWithin AI Agents, Automation, and AI Database Fields, you can choose from these preset models according to your needs. Preset models are optimized and fine-tuned by our team to deliver the highest performance, with credit consumption varying depending on the model tier.\n\nIn certain versions, you can even customize your own AI models by configuring different AI model providers and API key tokens. This allows you to minimize credit usage while tailoring models to your specific requirements.',
    ai_models_features_list: 'AI Models List',
    ai_translate_all: 'AI 翻译全部',
    artifact_code: '代码',
    artifact_outline: '大纲',
    artifact_preview: '预览',
    artifact_template_code: '模板代码',
    artifact_workflow: '工作流',
    completed: '已完成',
    generate: 'AI 生成',
    generate_image: '生成图像',
    generated_characters: '已生成 {characters} 个字符，完成 {percent}%',
    generated_result: '生成结果',
    generating: '生成中...',
    history: '历史记录',
    in_progress: '进行中',
    insert: '插入新内容',
    launcher_esc: '关闭窗口',
    launcher_open: '打开',
    launcher_select: '选择',
    launcher_tips_prefix: '使用',
    launcher_tips_suffix: '快捷键切换筛选器',
    load_more: '加载更多',
    name: 'AI 写作',
    new_chat: '新对话',
    overwrite: '替换原有内容',
    pick_an_image: '选择一张图像',
    press_enter: '按 Enter 发送, 按 Shift+Enter 换行',
    reference: '参考资料 {count}条',
    regenerate: '再次生成',
    restore_conversation: '恢复对话',
    share_conversation: '分享对话',
    slide_page_number: '第 {number} 页',
    slide_retry_count: '第 {count}/{max} 次尝试',
    slides_completed: '已完成',
    slides_generating: '正在生成幻灯片...',
    slides_generation_failed: '幻灯片生成失败，已重试多次',
    slides_pending: '等待开始...',
    slides_status_failed: '生成失败',
    slides_status_generating: '生成中...',
    start_chat: '开始聊天',
    thinking: '思考中...',
    thought: '思考了{seconds}秒',
    type_message: '在此输入消息',
    voice_hold_to_speak: '按住说话',
    voice_release_to_send: '松开发送',
  },
  ai_consultant: {
    deep_think: '深度思考',
    description:
      '我们是你的 AI 商业顾问团队。我们可以帮助你分析业务，生成解决方案建议并为你构建自动化 AI 系统',
    history: '历史记录',
    quick: '标准',
    replay: '回放',
    title: 'AI 应用顾问',
  },
  api: {
    api: 'API',
    create_token: '创建 API Token',
    create_token_description: '这些令牌允许其他应用程序控制您的整个帐户。请小心！',
    create_token_success: '创建令牌成功',
    created_public_api_token: '已经创建的公共 API Token',
    delete_token: '删除 Token',
    delete_token_description: '确定要删除这个 Token 吗？',
    delete_token_success: '删除令牌成功',
    developer_api: '开发者 API',
    e180days: '180 天',
    e1day: '1 天',
    e1month: '1 个月',
    e1year: '1 年',
    e2month: '2 个月',
    e30days: '30 天',
    e3days: '3 天',
    e6month: '6 个月',
    e7days: '7 天',
    expiration: '过期时间',
    my_tokens: '我的令牌',
    my_tokens_description: '您的 API 令牌需要像其他密码一样安全地处理。',
    name: '名称',
    never: '无期限',
    select_expiration: '选择有效期',
  },
  auth: {
    agree_description:
      '感谢您使用 Bika.ai。以下条款将帮助您了解用户和隐私政策，以及您享有的相关权利。您可以通过 {privacy} 和 {team} 查看完整内容。',
    agree_title: '请阅读并同意服务条款',
    auth_error: '认证错误',
    auth_error_description: '认证时出现错误，请稍后再试。',
    back: '返回',
    back_website: '返回官网首页',
    continue_with_apple: '使用 Apple 继续',
    continue_with_email: '使用电子邮箱继续',
    continue_with_facebook: '使用 Facebook 继续',
    continue_with_github: '使用 GitHub 继续',
    continue_with_google: '使用 Google 继续',
    continue_with_phone: '使用手机号继续',
    continue_with_twitter: '使用 Twitter 继续',
    continue_with_username: '使用账户名继续',
    continue_with_weixin: '使用微信继续',
    get_started: '开始使用',
    invite_you_to_join: '邀请你加入',
    invite_you_to_register: '{me} 邀请你注册 Bika.ai',
    link_account_already_exists:
      '该 {{type}} 账号已经绑定了其他 Bika 账号，请问是否直接登录已绑定账号',
    loading: '登录中...',
    login: '登录',
    login_and_register: '登录/注册',
    login_success: '登录成功',
    logout: '退出登录',
    no_permission:
      '抱歉，该邀请链接仅适用于企业内部用户。请使用您的企业邮箱登录或联系管理员获取有效的邀请链接',
    or: '或',
    password: '密码',
    password_is_required: '密码为必填项。',
    privacy_policy: '隐私政策',
    quick_login: '快速登录',
    quick_login_description: '快速注册一个新用户，无需密码和验证。',
    register_agreement: '注册即表示您同意我们的 {team} 和 {privacy}',
    scan_qrcode_to_login: '请用手机微信扫描二维码登录',
    sign_in: '登录',
    sign_up: '注册',
    switch_account: '切换账号',
    terms_and_conditions: '请阅读并接受我们的隐私政策和服务条款。我们致力于保护您的隐私和数据。',
    terms_of_service: '服务条款',
  },
  automation: {
    action: {
      actions: '执行器',
      ai_summary: {
        description: '通过 AI 模型，对文本内容进行摘要处理。',
        name: 'AI 文本摘要',
      },
      call_agent: {
        agent_id_subject: '代理人 ID',
        description: '拨打指定的代理人并发送消息。',
        message_subject: '消息',
        name: '拨打代理人',
        recipient: '接收者',
      },
      condition: {
        description: '根据条件判断结果，执行不同的动作。',
        name: '条件判断',
      },
      create_document: {
        description: '通过多个步骤和变量的组合动态生成文档。',
        name: '创建文档',
      },
      create_mission: {
        description: '创建新的任务，并派发给指定的成员、角色或小组。',
        name: '创建智能任务',
      },
      create_node_resource: {
        description: '通过多个步骤和变量的组合动态生成资源节点（数据库、文档、自动化、表单等)。',
        name: '创建资源节点',
      },
      create_record: {
        description: '在指定的数据表中，创建一条新的记录。',
        name: '创建记录',
      },
      deepseek_generate_text: {
        description: '使用 DeepSeek API 生成文本内容。',
        name: 'DeepSeek - 生成文本',
      },
      delay: {
        description: '暂停若干时间后，再执行下一步动作。',
        name: '延时',
        queue: '队列',
        unit: '单位',
        unit_day: '天',
        unit_hour: '小时',
        unit_minute: '分钟',
        unit_second: '秒',
        unit_week: '周',
        value: '值',
      },
      description: '备注',
      dingtalk_webhook: {
        description: '通过钉钉自定义机器人的 Webhook URL，发送消息到指定群。',
        message_title_description: '输入要发送的消息标题，该标题会显示在钉钉左侧消息列表中的摘要中',
        name: '发送消息至钉钉群',
      },
      dummy_action: {
        description: '用于测试和验证自动化流程。',
        name: '模拟动作',
      },
      feishu_webhook: {
        description: '通过飞书自定义机器人的 Webhook URL，发送消息到指定群。',
        name: '发送消息至飞书群',
      },
      filter: {
        description: '如果满足过滤条件，后续动作将继续执行。',
        name: '过滤器',
      },
      find_dashboard: {
        description: '查找指定的仪表盘。',
        name: '查找仪表盘',
      },
      find_members: {
        description: '根据筛选条件，查找符合条件的空间站成员。',
        name: '获取成员列表',
        to_email_addresses: '邮件地址',
        to_email_addresses_description: '多个邮件地址用逗号分隔，输入"/"可插入变量',
      },
      find_missions: {
        description: '查找指定的任务。',
        name: '查找任务',
      },
      find_records: {
        description: '从指定的数据表中，根据视图或筛选条件，获取若干记录。',
        find_database: '根据视图进行筛选',
        find_database_select: '根据数据表进行筛选',
        name: '获取记录',
        placeholder_select_type: '请选择查找方式',
        records_limit_description: '每次查找的最大记录数。设为1时仅返回单条记录。',
        records_limit_placeholder: '1 - 100',
        records_limit_title: '记录上限',
        title_interrupt_if_no_record: '未找到记录时，是否中断执行',
        type: '查找方式',
      },
      find_widget: {
        description: '查找指定的仪表盘组件。',
        name: '查找仪表盘组件',
        widget_empty: '组件不能为空',
      },
      formapp_ai: {
        create_and_load_formapp: '创建并加载 FormApp.ai 应用',
        description:
          'FormApp.ai 提供了大量的 AI 模型、执行器和扩展来帮助您自动化工作流程。您还可以在其上自定义您自己的 AI 模型、执行器和 API。',
        name: 'FormApp.ai',
      },
      loop: {
        abort_loop_help: '任意子执行器执行失败将立即终止整个循环，剩余迭代不再执行。',
        abort_loop_title: '失败时中止循环',
        add_action: '添加执行器',
        child_actions: '子级执行器',
        child_actions_help: '在每次循环迭代中按顺序执行的执行器。',
        description: '通过遍历数据集并根据预设条件触发相应的动作，用于自动化处理重复性任务。',
        name: '循环',
        retrieve: '从上游步骤中选择数组数据',
        retrieve_description: '选择上游步骤的输出数据进行迭代。仅支持数组类型的数据。',
        sequential_exec: '顺序执行',
        sequential_exec_help:
          '按严格的索引顺序处理数组元素，在完成当前迭代之后再执行下一次迭代。取消勾选则启用并行处理。',
      },
      not_found: '未找到可用的执行器',
      openai_generate_text: {
        apikey: 'API 密钥',
        description: '使用 OpenAI API 生成文本内容。',
        model: '模型',
        name: 'OpenAI - 生成文本',
        placeholder: '请选择模型',
        prompt: '提示 (Prompt)',
        timeout_label: '超时时间（单位：秒）',
        timeout_placeholder: '请输入超时时间，最大值为300秒，缺省时默认为60s',
      },
      random: {
        description: '将输入的若干选项，随机选择一个作为输出。',
        name: '随机',
      },
      replace_file: {
        description: '使用数据表中的记录批量替换文件。',
        name: '替换文件',
      },
      round_robin: {
        description: '将输入的若干选项，依次循环输出。',
        name: '轮询',
      },
      run_script: {
        description: '编写脚本代码，执行自定义的操作。支持Python、JavaScript。',
        name: '运行脚本',
      },
      send_email: {
        description: '发送邮件给指定的收件人。支持群发、自定义SMTP等功能。',
        name: '发送邮件',
        opens: '查看率',
        sent: '发送数',
      },
      send_report: {
        description: '生成一份报告，发送给指定的成员或群组。',
        name: '发送报告',
      },
      slack_webhook: {
        description: '通过 Slack 应用的 Incoming Webhook，发送消息到指定频道。',
        name: '发送消息至 Slack 频道',
      },
      telegram_send_message: {
        description: '通过 Telegram Bot，发送消息给指定用户或群组。',
        help_text_chat_id: '公共频道聊天 ID 需要以字符 "@" 开头，例如 @channel_name',
        name: '发送消息至 Telegram',
      },
      toolsdk_ai: {
        create_and_load_toolsdk: '创建并加载 ToolSDK.ai 应用',
        description:
          'ToolSDK.ai 提供了 2000+ 个 MCP 服务器和 10000+ 个 AI 工具。让你可以在自动化流程中轻松使用第三方 AI 能力，快速扩展工作流功能。',
        name: 'MCP Server (by ToolSDK.ai)',
      },
      twitter_upload_media: {
        description: '通过 X(Twitter) API 上传媒体文件，支持图片和视频。',
        media: '媒体URL',
        name: 'X(Twitter) - 上传媒体文件',
      },
      type: '类型',
      update_record: {
        description: '在指定的数据表中，更新一条或多条记录。',
        name: '更新记录',
      },
      webhook: {
        description: '发起一个 HTTP 请求，与其他系统进行数据交互。',
        edit: '编辑请求体',
        name: '发送 HTTP 请求',
      },
      wecom_webhook: {
        description: '通过企业微信群机器人的 Webhook URL，发送消息到指定群。',
        name: '发送消息至企业微信群',
      },
      x_create_tweet: {
        auth_method: '认证方法',
        auth_method_help_text:
          '不同的认证方法需要使用不同的集成实例，请切换认证方法后重新选择集成类型。',
        auth_method_tooltip: '了解更多关于OAuth 1.0a和OAuth 2.0之间的区别',
        description: '通过 X(Twitter) API，发布一条推文。',
        media_ids: '图片或视频',
        media_ids_help_text:
          '给推文添加图片或视频，请使用“上传媒体文件”执行器获取媒体id，然后通过变量选择器插入至此处。',
        name: 'X(Twitter) - 创建推文',
        tweet_content: '推文内容',
        x_account_integration: 'X(Twitter)账户集成',
      },
    },
    action_help_urls: {
      DINGTALK_WEBHOOK: '/help/reference/automation-action/dingtalk-webhook',
      DINGTALK_WEBHOOK_MARKDOWN: 'https://open.dingtalk.com/document/isvapp/message-type',
      FEISHU_WEBHOOK: '/help/reference/automation-action/feishu-webhook',
      FEISHU_WEBHOOK_MARKDOWN:
        'https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot#5a997364',
      SEND_EMAIL: '/help/reference/integration/smtp-email-account',
      SLACK_WEBHOOK: '/help/reference/automation-action/slack-webhook',
      SLACK_WEBHOOK_MARKDOWN: 'https://api.slack.com/reference/surfaces/formatting',
      TELEGRAM_SEND_MESSAGE: '/help/reference/automation-action/telegram-send-message',
      TELEGRAM_SEND_MESSAGE_MARKDOWN: 'https://core.telegram.org/bots/api#formatting-options',
      TWITTER_UPLOAD_MEDIA: '/help/reference/automation-action/twitter-upload-media',
      WEBHOOK: '/help/reference/automation/webhook-action',
      WECOM_WEBHOOK: '/help/reference/automation-action/wecom-webhook',
      WECOM_WEBHOOK_MARKDOWN:
        'https://developer.work.weixin.qq.com/document/path/91770#markdown%E7%B1%BB%E5%9E%8B',
      WECOM_WEBHOOK_TEMPLATE_CARD:
        'https://developer.work.weixin.qq.com/document/path/91770#%E6%A8%A1%E7%89%88%E5%8D%A1%E7%89%87%E7%B1%BB%E5%9E%8B',
      X_CREATE_TWEET: '/help/reference/automation-action/x-create-tweet',
    },
    action_type_intro: {
      DINGTALK_WEBHOOK: '通过钉钉自定义机器人的 Webhook URL， 发送消息到指定群。',
      FEISHU_WEBHOOK: '通过飞书自定义机器人的 Webhook URL， 发送消息到指定群。',
      SLACK_WEBHOOK: '通过 Slack 应用的 Incoming Webhook， 发送消息到指定频道。',
      TELEGRAM_SEND_MESSAGE: '通过 Telegram Bot，发送消息给指定用户或群组。',
      WEBHOOK: '构造一个自定义的 HTTP 请求，发送到指定的 URL。',
      WECOM_WEBHOOK: '通过企业微信群机器人的 Webhook URL， 发送消息到指定群。',
      X_CREATE_TWEET: '通过 Twitter API，发布一条推文。',
    },
    add_action: '添加执行器',
    add_automation: '添加自动化',
    add_trigger: '添加触发器',
    advanced_options: '进阶选项',
    automation: '自动化任务',
    cancel_delay: '终止延时运行',
    cancel_delay_content: '终止后将不再执行延时的后续步骤，确定要终止运行吗？',
    cancel_delay_success: '延时运行已终止',
    choose_imap_integration: 'IMAP 邮箱集成',
    choose_integration: '选择已有的集成',
    close_automation: '关闭自动化',
    closed: '已关闭',
    coming_soon_description: '此功能即将推出！任何建议都可以让我们加快速度！',
    coming_soon_feedback: '感谢您的反馈！',
    coming_soon_placeholder: '请在此留下您的反馈，非常感谢！',
    copy_of: '{name}的副本',
    description_empty: '未设置描述',
    description_help_text: '请填写简洁明了的备注，这将会在自动化主界面上显示，以便于理解。',
    disabled: ' 关',
    drop_files_here: 'Drop files here',
    duplicate_success: '复制成功',
    email_content_type_label: '邮件内容类型',
    empty_history: '暂无运行历史',
    empty_step: '未设定自动化步骤',
    empty_step_log: '此步骤无历史日志',
    enable: '启用',
    enabled: '开',
    history: '运行历史',
    history_status: {
      cancelled: '取消运行',
      delay: '延时',
      failed: '运行失败',
      running: '正在运行',
      success: '运行成功',
      timeout: '运行超时',
    },
    http_if_change: {
      every_failed: '请求失败每次触发',
      first_failed: '请求首次失败触发',
      policy_title: '策略',
      response_changed: '请求成功后，响应值发生变化触发（失败不触发）',
      response_compare_json_path: '响应值比较路径',
    },
    inbound_email: {
      description_guide: '如何写搜索条件？',
      label_mailbox_archive: '归档',
      label_mailbox_deleted: '已删除',
      label_mailbox_drafts: '草稿箱',
      label_mailbox_inbox: '收件箱',
      label_mailbox_junk: '垃圾箱',
      label_mailbox_sent: '已发送',
      placeholder_mailbox_name: '输入文件夹名称，默认是收件箱。',
      placeholder_search_criteria: '输入邮件抓取规则',
      search_rule_description: '更多详细信息请参阅 {help} 的搜索功能，不填写默认为信箱的所有邮件。',
      title_download_attachments: '下载附件',
      title_mailbox_name: '文件夹',
      title_search_criteria: '自定义邮件搜索条件',
      toast_imap_connection_error: 'IMAP 连接失败，请检查配置',
    },
    input: '输入',
    item_output: '循环项输出',
    label: {
      manual_input: '手动输入URL',
      select_integration: '从集成中提取URL',
    },
    manual_execution_description: '确定要立即运行此自动化流程吗?',
    manual_execution_success: '自动化流程已开始运行',
    manual_trigger_fields: '动态字段',
    manual_trigger_fields_desc:
      '自动化运行之前需要动态传入的字段。这些字段可以在整个工作流中被任意执行器通过变量的方式引用。',
    manual_trigger_result: '运行结果',
    manual_trigger_result_desc: '测试此步骤以确认其配置正确',
    max_trigger_count: '触发器最多只能添加3个',
    no_description: '未设置描述',
    off: '关闭',
    on: '自动',
    open_automation: '开启自动化',
    output: '输出',
    parameter_source: '参数来源',
    parameter_source_description: '你可以选择手动配置参数或复用所选集成的设置。',
    parameter_source_option1: '手动配置',
    parameter_source_option2: '使用已有集成配置',
    placeholder_choose_integration: '点击选择或添加',
    recent_history: '最近一次运行历史',
    recent_history_detail: '查看详情',
    recent_history_id: '运行历史 ID',
    recent_history_start_time: '开始时间',
    recent_history_status: '当前状态',
    record_list: '记录列表',
    repeat_for_each_in: '对列表中的每一项依次执行下方步骤',
    report_content_description:
      '报告内容支持 Markdown、HTML 格式，可以使用 Markdown 语法进行编辑。',
    report_content_type_label: '报告内容类型',
    report_markdown: '报告内容',
    report_markdown_placeholder: '请输入报告内容',
    report_prompt: '报告提示',
    report_prompt_placeholder: '请输入报告提示',
    report_subject: '报告主题',
    report_subject_placeholder: '请输入报告主题',
    report_type: '报告类型',
    round_robin: {
      label: {
        select_action: '选择执行器',
        select_database: '选择数据表',
        select_target: '目标',
        select_view: '选择视图',
      },
      type: {
        database: '数据表',
        database_view: '数据表视图',
        prev_action: '上游的执行器',
        round_type: '轮询类型',
        user: '用户',
      },
    },
    run: {
      failed: '失败',
      status: '运行{status}，时间：{time}',
      success: '成功',
    },
    run_immediately: '立即运行',
    run_immediately_description: '自动化将立即运行，仅运行一次',
    run_test: {
      description: '运行测试以验证自动化流程的触发器和执行器是否正常工作。',
      preview_button: '预览上次运行结果',
      run_button: '运行测试',
      title: '运行测试',
    },
    script: '脚本',
    script_language: '代码语言',
    send_message_to_slack: '发送消息到 Slack',
    sort: '排序',
    started: '已开始',
    status: '状态',
    then: '那么',
    title_manual_execution: '运行自动化流程',
    tooltip_learn_more: '了解更多',
    tooltips: {
      step_error: '当前步骤中缺少必需的配置项，此步骤可能无法正常工作，请检查',
    },
    trigger: {
      add_action: '添加执行器',
      add_trigger: '添加触发器',
      button_clicked: {
        description: '点击数据表按钮字段中的按钮时执行操作。',
        name: '按钮字段被点击',
      },
      datetime_field_reached: {
        ahead: '提前',
        days: '天数',
        delay: '推后',
        description: '当记录中的某个日期字段接近或到达特定日期时自动执行操作。',
        hours: '小时',
        minutes: '分钟',
        name: '日期字段满足条件',
        offset: '偏移量',
        offset_day: '提前或延后若干天',
        offset_day_placeholder: '负数表示提前的天数，正数表示延后的天数，0 表示当天',
        offset_hour: '提前或延后若干小时',
        offset_hour_placeholder: '负数表示提前的小时数，正数表示延后的小时数，0 表示当前小时',
        offset_minute: '提前或延后若干分钟',
        offset_minute_placeholder: '负数表示提前的分钟数，正数表示延后的分钟数，0 表示当前分钟',
        today: '当天',
      },
      description: '备注',
      dummy_trigger: {
        description: '用于测试和验证自动化流程触发条件。',
        name: '模拟触发',
      },
      form_submitted: {
        description: '当有新的表单提交时自动触发。',
        name: '有新的表单提交',
      },
      http_if_change: {
        description: '当检测到 HTTP 响应内容发生变化时自动触发。',
        name: 'HTTP 响应体发生变更',
      },
      inbound_email: {
        description: '当收到特定邮件时自动触发。',
        name: '收到特定邮件',
      },
      manually: {
        description: '用户手动点击触发时执行操作。',
        name: '手动触发',
      },
      member_joined: {
        description: '新成员加入空间站时执行操作。',
        name: '有新的成员加入',
      },
      not_found: '触发器未找到',
      record_created: {
        description: '数据表新增记录时执行操作。',
        name: '有新的记录创建',
      },
      record_match: {
        description: '新增或编辑的记录满足条件时执行操作。',
        name: '有记录满足条件',
      },
      scheduler: {
        at_least_one_option: '至少选择一个选项',
        description: '当达到设定时间时自动执行操作。',
        name: '定时任务',
      },
      select_database: '选择数据表',
      select_form: '选择表单',
      select_form_have_not: '当前没有表单可供选择',
      select_form_note: '该触发器需要选择表单才可以完成配置',
      select_form_placeholder: '请选择一张表单',
      select_match: '匹配条件',
      triggers: '触发器',
      type: '触发器类型',
      webhook_received: {
        description:
          '当外部系统需要将数据发送到 Bika.ai 时使用。Webhook 提供一个唯一的网址链接，允许第三方应用程序（如电商平台、CRM系统等）在特定事件发生时自动向 Bika.ai 传递信息，从而触发相应的自动化流程。',
        name: 'Webhook 触发',
        placeholder: '保存后将生成唯一的 Webhook 链接',
        url: 'Webhook 链接',
      },
    },
    trigger_creation_success: '触发器创建成功',
    trigger_help_urls: {
      form_submit: '/help/reference/node-resource/form',
      inbound_email: '/help/reference/automation-trigger/inbound-email',
    },
    trigger_history_empty: '此次运行由用户手动触发，无触发器历史',
    trigger_update_success: '触发器更新成功',
    updating: '更新中',
    variable: {
      placeholder: '输入 "/" 插入变量',
    },
    variable_select: {
      automation: {
        id: '自动化 ID',
        interrupt_url: '中断延迟自动化请求URL',
        name: '自动化名称',
        run_history_id: '运行ID',
      },
      choose: '选择',
      createdAt: '创建时间',
      data_empty: '暂无数据',
      database: {
        id: '数据库 ID',
        name: '数据库名称',
        url: '数据库 URL',
      },
      databaseId: '数据表 ID',
      database_title: '数据表',
      field: {
        clear_all: '清空',
        data: '字段原始数据',
        data_tips: '字段原始数据，用于更新记录或脚本运算。',
        doc_id: '文档 ID',
        doc_name: '文档名称',
        id: '字段 ID',
        join: '连接字段',
        name: '字段名称',
        select_all: '全选',
        type: '字段类型',
        value: '字段值',
        value_tips: '格式化后的字段数据，适用于界面显示或插入至邮件/文本。',
      },
      fields: '字段',
      getting_variable: '获取变量中...',
      global: {
        key: '全局',
        no_result_found: '暂无搜索记录',
        official_website: '官方网站',
      },
      id: 'ID',
      insert: '插入',
      item: '当前项',
      item_actions: '当前执行器',
      item_description: '从循环列表中获取',
      item_title: '循环数据源',
      member_email: '成员邮箱',
      member_id: '成员ID',
      member_ids: '成员ID列表',
      member_ids_tips: '由成员ID组成的数组，可使用循环执行器逐一处理',
      member_name: '成员名',
      member_user_id: '成员用户ID',
      members: '成员列表',
      members_length: '成员数',
      members_tips: '多名成员组成的合集，可使用循环执行器逐一处理',
      menu_insert_variable_title: '插入变量',
      name: '名称',
      other_title: '其他',
      placeholder: '选择变量',
      primary_component: '原组件',
      record: {
        cell: '单元格',
        grid_list: '网格列表(文本)',
        grid_tips: '将记录格式化为文本表格',
        id: '记录 ID',
        list_tips: '将记录格式化为文本列表',
        record_id_list: '记录 ID 列表',
        record_id_list_tips: '由记录ID组成的数组，可使用循环执行器逐一处理',
        record_list: '记录列表(文本)',
        record_tips: '当前操作的记录，可以单独选择其中的某个字段',
        records: '记录',
        records_length: '记录数',
        records_tips: '由多个记录组成的数组，可使用循环执行器逐一处理',
        selected_fields: '选中的字段',
        url: '记录 URL',
      },
      recordId: '记录 ID',
      record_title: '记录',
      resume_time: '恢复时间​',
      resume_time_tips: '流程将在此时从当前步骤自动继续执行',
      revision: '版本',
      round_robin_item: '轮询项',
      run_time: '执行时间',
      run_time_tips: '自动化流程被触发的时间',
      select_data_placeholder: '选择数据',
      select_data_title: '选择数据',
      select_variable: '选择',
      space: {
        home_page_url: '空间站主页 URL',
        id: '空间站ID',
        name: '空间站名称',
        report_page_url: '我的报告页面 URL',
        todo_page_url: '我的待办页面 URL',
      },
      updatedAt: '更新时间',
      url: 'URL',
      variable_component: '变量组件',
    },
    webhook: {
      add_field: '添加字段',
      add_header: '添加请求头',
      description_guide: '配置教程',
      description_message_content: '支持 Markdown 格式，输入"/"可插入变量',
      description_webhook_source:
        '当前执行器需要输入 Webhook URL 作为消息发送的目标地址。你可选择手动填入Webhook URL，也可以从空间站已有的集成中选择一个进行复用。',
      feishu_type_interactive: '消息卡片',
      feishu_type_post: '富文本',
      feishu_type_text: '文本消息',
      field_value: '字段值',
      message_type_actioncard: 'ActionCard',
      message_type_link: '链接',
      message_type_templatecard: '模板卡片',
      message_type_text: '文本消息',
      placeholder_field_name: '输入字段名称',
      placeholder_field_value: '输入字段值',
      placeholder_header_name: '名称',
      placeholder_header_value: '值',
      placeholder_request_method: '选择请求方法',
      placeholder_request_url: '输入请求URL',
      placeholder_webhook_source: '请选择一种Webhook来源',
      placeholder_webhook_url: '请输入Webhook URL',
      support_format: '支持的格式',
      title_body_type: '请求体类型',
      title_content_type: '内容类型',
      title_form_data: '表单数据',
      title_message_content: '消息内容',
      title_message_title: '消息标题',
      title_message_type: '消息类型',
      title_request_content: '请求内容',
      title_request_headers: '请求头（Headers）',
      title_request_method: '请求方法',
      title_request_url: 'URL',
      title_webhook_source: 'Webhook 来源',
      title_webhook_url: 'Webhook URL',
      webhook_json_error: 'JSON 格式错误',
    },
    when: '当',
  },
  avatar: {
    avatar: '头像',
    cancel_select: '取消选择',
    change_avatar: '更改头像',
    edit_image: '编辑',
    file_tip: '支持 JPG、PNG 和 GIF 格式，图片大小需在 2 MB 以内',
    gif_no_crop_tip: 'GIF 文件将不进行裁剪以保持动画效果',
    paste_image_link: '粘贴图片链接...',
    preview: '预览',
    preview_avatar: '头像预览',
    re_select: '重新选择',
    select_from_gallery: '从相册选择',
    tab_color: '颜色',
    tab_link: '链接',
    tab_link_tip: '适用于网络上的任何图像。',
    tab_upload: '上传',
    take_photo: '拍照',
    upload_avatar: '上传图片',
  },
  brand: {
    about_brand: '关于 Bika.ai',
    brand: 'Bika.ai',
    website: '官方网站',
  },
  buttons: {
    add: '添加',
    add_virtual_intelligent_task: '添加虚拟智能任务',
    back: '返回',
    back_to_space: '返回我的空间站',
    cancel: '取消',
    change_bound_email: '更改绑定邮箱',
    close: '关闭',
    completed: '已完成',
    confirm: '确认',
    create: '新建',
    create_space: '创建空间站',
    delete: '删除',
    edit: '编辑',
    more: '更多...',
    pre_fill_title_btn: '预填充',
    remove: '移除',
    run: '运行',
    save: '保存',
    see_more: '查看更多',
    send_your_suggestion: '提交您的建议',
    submit: '提交',
    view_all: '查看全部',
  },
  cancel: '取消',
  coming_soon: '敬请期待',
  components: {
    breadscrumb: {
      root: '根目录',
    },
    configure_multilingual: '配置多语言',
    confirm_remove_multilingual_configuration: '确认要删除多语言配置吗？',
    disable_multilingual_warning: '关闭后将清空原有内容',
    enable_multilingual_warning: '开启将清空内容重新配置语言',
    remove_multilingual_configuration_warning:
      '一旦删除后，则不能恢复，只保留一种语言，如需增加需要重新配置。',
    view_all_languages: '查看所有语言',
  },
  confirm: '确认',
  copy: {
    copy: '复制',
    copy_link: '复制链接',
    copy_link_to_clipboard: '复制链接到剪贴板',
    copy_success: '复制成功',
    create_short_url: '创建短链接',
    delete_short_url: '删除短链接',
  },
  dashboard: {
    add_widget: '添加组件',
    select_data_source: '选择数据源',
    widget_not_editable: '该组件不支持修改。',
  },
  dashboard_widgets: {
    ai_widget: {
      description: 'A Widget that AI-generated content',
      name: 'AI Widget',
    },
    bika: {
      description: '显示bika的组件',
      name: 'bika',
    },
    chart: {
      description: '将表格内的数据可视化为柱状图、条形图、折线图、散点图、饼状图等多种展示形式',
      name: '图表',
    },
    embed: {
      description: '输入一个网址即可将其他网站的内容嵌入',
      name: '嵌入',
    },
    icons: {
      description: '显示图标的组件',
      name: '图标',
    },
    list: {
      description: '显示列表的组件',
      name: '列表',
    },
    number: {
      description: '统计表格内任一列数据，并将统计值以突出的样式显示在组件上',
      name: '高亮数字',
    },
    pivot_table: {
      description: '一个快速对明细数据表进行分类汇总的数据分析工具',
      name: '透视表',
    },
    progress_bar: {
      description: '显示进度条的组件',
      name: '进度条',
    },
    text: {
      description: '显示文本的组件',
      name: '文本内容',
    },
  },
  data: {
    data: '数据',
    database: '数据表',
  },
  database_fields: {
    ai_photo: {
      description: 'AI自动生成的图片和图像内容，适用于产品展示、社交媒体等场景',
      name: 'AI图片',
    },
    ai_text: {
      ai_write: 'AI 生成',
      ai_writing: 'AI 生成中...',
      auto_update: '自动更新',
      description:
        'AI自动生成文本内容，可引用表内数据，适用于客服回复、产品描述、内容摘要等场景，提升创作效率',
      llm_provider: 'AI 服务提供商',
      name: 'AI文本',
      preview_btn: '生成预览',
      preview_empty: '暂无预览',
      preview_result: '预览结果',
      preview_result_description: '此预览基于此表中的第一条记录',
      prompt_description: '告诉 AI 应该如何生成消息内容，输入"/"可插入表中的字段',
      prompt_menu_title: '选择字段',
      prompt_title: '提示（Prompt）',
    },
    ai_video: {
      description: 'AI自动生成的视频和动画内容，适用于广告宣传、社交媒体等场景',
      name: 'AI视频',
    },
    ai_voice: {
      description: 'AI自动生成的语音和音频内容，适用于语音助手、播客等场景',
      name: 'AI语音',
    },
    api: {
      description: '存储API接口信息，用于与外部系统交互和数据交换',
      name: 'API',
    },
    attachment: {
      adaptive: '自适应',
      close: '关闭',
      copy_link: '复制链接',
      copy_link_success: '复制链接成功',
      delete: '删除',
      description: '允许上传和存储各种类型的文件作为记录的附件，如文档、图片或压缩包',
      download: '下载',
      download_success: '下载成功',
      initial_size: '初始大小',
      name: '附件',
      rotate: '旋转',
      zoom_in: '放大',
      zoom_out: '缩小',
    },
    auto_number: {
      description: '自动为每条新记录生成唯一的序列号，适用于订单编号、工单号等场景',
      name: '自动编号',
    },
    base: {
      field_description: '字段描述',
      field_name: '字段名称',
      field_type: '字段类型',
      field_type_placeholder: '请选择列类型',
    },
    button: {
      description: '创建可点击的交互式按钮，点击后可触发预设的自动化操作或事件',
      name: '按钮',
    },
    cascader: {
      description: '提供多级联动的下拉选择菜单，适用于有层级关系的数据选择，如地区选择',
      name: '级联选择',
    },
    checkbox: {
      description: '提供是/否选项的复选框，适用于状态标记或简单的布尔值选择',
      name: '复选框',
    },
    created_by: {
      description: '自动记录创建该条记录的用户信息，便于追踪记录来源',
      name: '创建者',
    },
    created_time: {
      description: '自动记录该条记录被创建的具体日期和时间',
      name: '创建时间',
      property: {
        auto_fill: '自动填充',
        date_format: '日期格式',
        date_format_placeholder: '请选择日期格式',
        show_time: '显示时间',
        time_format: '时间格式',
      },
    },
    currency: {
      description: '专门用于存储和格式化货币金额，支持不同货币符号和精度设置',
      name: '货币',
      property: {
        accuracy: '精度',
        alignmen_default: '默认对齐',
        alignmen_left: '左对齐',
        alignmen_right: '右对齐',
        alignment: '符号对齐方式',
        symbol: '符号',
        symbol_placeholder: '请输入货币符号',
      },
    },
    cut_video: {
      description: '存储经过剪辑或编辑的视频片段，保留编辑信息和时间轴',
      name: '视频剪辑',
    },
    daterange: {
      description: '存储时间段或日期范围，包含开始和结束两个时间点，适用于项目周期、活动时间等',
      name: '日期范围',
      property: {
        date_format: '日期格式',
        date_format_placeholder: '请选择日期格式',
        show_time: '显示时间',
        time_format: '时间格式',
      },
    },
    datetime: {
      description: '存储精确的日期和时间信息，适用于需要记录确切时间点的场景',
      name: '日期时间',
      property: {
        auto_fill: '新增记录自动填写创建时间',
        date_format: '日期格式',
        date_format_placeholder: '请选择日期格式',
        show_time: '显示时间',
        time_format: '时间格式',
      },
      repeat_day: '按天',
      repeat_hour: '按小时',
      repeat_minute: '按分钟',
      repeat_month: '按月',
      repeat_week: '按周',
      repeat_year: '按年',
    },
    email: {
      description: '专门用于存储电子邮件地址，适用于联系人信息、通知等场景',
      name: '邮箱',
    },
    formula: {
      description: '通过公式自动计算值，可引用其他字段并进行数学或逻辑运算',
      name: '公式',
      property: {
        expression: '公式',
        expression_placeholder: '请输入公式',
      },
    },
    json: {
      description: '存储结构化的JSON格式数据，适用于复杂数据结构或API响应内容',
      name: 'JSON',
    },
    link: {
      description: '创建与其他表的双向关联，实现表间数据的互相引用和关系维护',
      name: '关联',
      property: {
        relation_database: '关联表',
      },
    },
    long_text: {
      description: '用于存储长篇文本内容，如详细描述、注释或文章正文',
      name: '多行文本',
    },
    lookup: {
      description: '从关联表中自动查找并显示特定字段的值，实现数据的动态引用',
      name: '查找引用',
      property: {
        error: '未找到对应的数据库',
        lookup_field: '查询的字段',
        select_link_database: '选择关联表',
      },
    },
    member: {
      description: '存储系统成员信息，可选择单个或多个成员作为字段值',
      name: '成员',
      property: {
        allow_multiple: '允许添加多个成员',
        notify_mentioned: '选择成员后发送消息通知',
      },
    },
    modified_by: {
      description: '自动记录最后一次修改该记录的用户信息',
      name: '修改者',
    },
    modified_time: {
      description: '自动记录该记录最后一次被修改的日期和时间',
      name: '修改时间',
      property: {
        auto_fill: '自动填充',
        date_format: '日期格式',
        date_format_placeholder: '请选择日期格式',
        show_time: '显示时间',
        time_format: '时间格式',
      },
    },
    multi_select: {
      description: '从预定义的选项列表中选择多个选项，适用于多标签分类',
      name: '多选',
      property: {
        add_options: '添加一个选项',
        default_value: '默认值',
      },
    },
    number: {
      description: '存储数值型数据，支持整数和小数，可设置精度和格式',
      name: '数字',
      property: {
        custom_units: '自定义单位',
        custom_units_default: '请输入单位名称',
        precision: '精度',
        thousand_separator: '千分位',
      },
    },
    one_way_link: {
      description: '创建与其他表的单向关联，只能从当前表查看关联表的数据',
      name: '单向链接',
    },
    percent: {
      description: '存储百分比值，自动格式化显示为百分比形式，适用于进度、比率等场景',
      name: '百分比',
      property: {
        default_value: '默认值',
        precision: '精度',
      },
    },
    phone: {
      description: '专门用于存储电话号码，适用于联系人信息、客户资料等场景',
      name: '电话',
    },
    photo: {
      description: '存储和显示图片文件，支持预览和缩略图功能',
      name: '照片',
    },
    rating: {
      description: '以星级或数值形式存储评分信息，可视化展示评价等级',
      name: '评分',
      property: {
        icon_settings: '图标设置',
        max_value: '最大值',
      },
    },
    single_select: {
      description: '从预定义的选项列表中选择单一选项，适用于状态或分类场景',
      name: '单选',
      property: {
        add_options: '添加一个选项',
        default_value: '默认值',
      },
    },
    single_text: {
      description: '存储简短的单行文本，适用于标题、名称等简洁信息',
      name: '单行文本',
    },
    url: {
      description: '存储网页链接地址，支持直接跳转链接进行访问',
      name: '网址',
    },
    video: {
      description: '存储视频文件，支持上传、预览和播放功能',
      name: '视频',
    },
    voice: {
      description: '存储音频文件，支持录制、上传和播放功能',
      name: '语音',
    },
    work_doc: {
      description: '存储支持Markdown格式的富文本文档，可直接在单元格内创建和编辑文档内容，',
      name: '文档',
    },
  },
  database_views: {
    form: {
      description:
        '表单视图允许用户创建自定义表单，方便数据输入和收集。用户可以通过分享表单链接，收集来自外部用户的数据，自动将收集的数据添加到系统中。适用于需要简化数据输入和收集过程的场景',
      name: '表单',
    },
    gallery: {
      description:
        '相册视图是将记录以卡片的形式展示，以记录附件中的图片作为封面，适用于名片、物料和菜单等场景',
      name: '相册',
    },
    gantt: {
      description:
        '甘特视图以时间轴的方式展示项目进度，用户可以直观地查看任务的开始和结束时间以及各任务之间的依赖关系。适用于需要有效计划和管理项目进度的场景',
      name: '甘特图',
    },
    kanban: {
      description:
        '看板视图以卡片形式展示数据，每一列代表一个状态或分类。用户可以通过拖放卡片在不同列之间移动来反映任务或项目的进展情况。适用于需要直观跟踪工作流程和任务进度的场景',
      name: '看板',
    },
    table: {
      description:
        '表格视图提供了一种电子表格样式的布局，用户可以以结构化的方式查看和管理数据。每一列代表一个字段，每一行代表一条记录，便于快速浏览、筛选和排序数据。适用于需要清晰、有条理地管理和操作大批量数据的场景',
      name: '表格',
    },
  },
  delete: {
    confirm_deletion: '确认删除',
    confirm_to_delete_content: '确定要删除 {name} 吗?',
    confirm_to_delete_this_link: '确认删除此链接?',
    confirm_to_delete_title: '确认是否删除',
    delete: '删除',
    delete_success: '删除成功',
  },
  document: {
    code_placeholder: '输入代码',
    list_placeholder: '输入列表项',
    status_connected: '已连接',
    status_connecting: '连接中',
    status_disconnected: '未连接',
    text_placeholder: '输入 "/" 插入',
    title_placeholder: '请输入标题',
  },
  editor: {
    add_button_text: '添加',
    add_column_as_sort_condition: '可以添加引用字段或引用字段所在表任意字段作为排序条件',
    add_condition: '添加条件',
    add_filter_condition: '设置筛选条件',
    add_skill: '添加',
    add_sort_condition: '设置排序条件',
    aggegate_records: '对引用数据进行统计',
    all_count: '全部计数',
    all_record: '全部',
    and_cal: '与运算',
    and_condition: '并且 “{field}” {condition} “{value}”',
    approval: '审批',
    average: '平均值',
    button_refresh: '刷新',
    by_filter_and_sort: '对引用字段进行筛选和排序',
    card_style: '卡片样式',
    collapse: '折叠',
    collapse_all_group: '折叠所有分组',
    collapse_group: '折叠分组',
    column_required: '{name}是必填字段',
    concat_as_text: '连接成文本',
    concat_by_semicon: '用分号连接',
    condition: '当 “{field}” {condition} “{value}”',
    content_paste_failure: '粘贴失败',
    content_paste_successfully: '粘贴成功',
    count_sort_and_filter_confition:
      '{filterConditionCount}个筛选条件和{sortConditionCount}个排序条件',
    create_mission_line_text: '创建智能任务',
    create_record_line_text: '创建记录',
    current_data_not_allowed: '数据无法写入该单元格',
    custom_skill: '自定义技能',
    data_import_process: '{Percent} 数据上传中，暂时不可更新数据',
    delete_n_records_in_list: '删除列表所选{count}条记录',
    delete_record_warning_content: '您确定要删除吗',
    enable: '启用',
    excel_import_success: '已成功导入{Count}条数据',
    expand_all_group: '展开所有分组',
    expand_group_or_sub_group: '展开分组/子分组',
    filter_no_nil: '过滤所有空值',
    find_records_line_text: '查找',
    first_record: '第一条记录',
    grid_row_height: '行高',
    grid_row_height_default: '默认',
    grid_row_height_extra_large: '超高',
    grid_row_height_large: '高',
    grid_row_height_medium: '中等',
    grid_row_height_seeting: '高度设置',
    integration_line_text: '配置集成',
    item_not_supported_currently: '该选项暂不支持',
    lookup_count: '引用数量',
    lookup_original_values: '从关联表原样引用数据',
    maximal_value: '最大值',
    microphone_disabled: '麦克风使用权限被拒绝。请更新浏览器的权限设置后重试。',
    minimal_value: '最小值',
    no_search_result: '暂无匹配搜索结果',
    not_nil_count: '非空数值计数',
    not_null_count: '非空值计数',
    number_value_format: '数值格式',
    operate_successfully: '操作成功',
    or_calulate: '或运算',
    or_condition: '或者 “{field}” {condition} “{value}”',
    original_values: '原始值',
    please_add_link_field: '请先添加关联字段(LINK, ONEWAY_LINK)',
    please_input_data: '请输入数据',
    please_select_a_skillset: '请先选择一个技能集',
    please_select_at_least_one_skill: '请至少选择一个技能',
    please_select_configuration: '请选择配置',
    record_filter_out_tips: '被筛选过滤的记录不会被引用',
    remove_dulplicated: '去重',
    select_skill: '请为 AI 智能体添加技能',
    show_cover: '显示封面',
    show_field_name: '显示字段名称',
    show_field_name_help_text: '在卡片上显示字段名称。如果禁用，则只显示字段值。',
    show_logo: '显示Logo',
    show_time_zone_info: '显示时区信息',
    skillsets: '技能集',
    start_paste: '正在粘贴...',
    stop_microphone: '停止',
    sum_value: '总和',
    sum_value_tooltip: '返回所有值的总和n SUM(1, 3, 5, "", "Apple") => 9 (1+3+5)',
    symbol_align_strategy: '符号对齐方式',
    table_lock_message: '暂无法更新，请稍后再试',
    text_area_tips: 'Enter 换行, Shift + Enter 结束编辑',
    this_field_configuration_missing: '该字段存在配置错误。请检查公式或字段使用的配置。',
    this_field_not_allow_edit: '该字段不允许被编辑',
    upload_image: '上传图片',
    upload_image_banner_size: '建议尺寸: 1440*480',
    use_micro_phone: '使用麦克风',
    xor_calculate: '异或运算',
    you_have_no_saved_change: '未保存的更改',
    you_have_no_saved_change_content: '你确定要关闭此窗口吗？你的更改将不会被保存。',
  },
  email: {
    bcc: '密送',
    body: '正文',
    cc: '抄送',
    from_email: '发件人邮箱',
    from_name: '发件人名称',
    help_text_reply_to: '收件人回复邮件时，会发送至此邮箱地址。',
    provider: {
      service: 'Bika 电子邮件服务',
      smtp: '自定义 SMTP',
      smtp_integration: '使用 SMTP 集成',
    },
    provider_type: '发送方式',
    recipient: '收件人',
    reply_to: '回复至',
    send_email: '发送邮件',
    smtp_host: 'SMTP 主机',
    smtp_password: 'SMTP 密码',
    smtp_port: 'SMTP 端口',
    smtp_username: 'SMTP 用户名',
    subject: '主题',
  },
  error: {
    back: '返回上一页',
    back_to_home: '返回主页',
    error: '错误',
    error_code: '错误代码',
    error_description: '错误描述',
    error_message: '错误消息',
    export: {
      record_limit: '当前数据表记录超过 50,000 行，无法进行导出操作',
    },
    node_server_error: '当前节点被删除或者不存在',
    oops: '哎呀!',
    page_error: '页面发生了一些错误',
    page_not_found: '未找到页面',
    page_not_found_description: '抱歉，我们找不到您正在寻找的页面。尝试后退到上一页？',
    screen_not_found: '未找到屏幕',
    space: {
      back: '回到我的空间站',
      description: '请确认链接是否正确，并确保你有访问权限，如有疑问请联系任务发布者',
      title: '无权限查看该链接内容',
    },
  },
  explore: {
    explore: '探索',
  },
  filter: {
    and: '并且',
    contains: '包含',
    date_after_or_equal: '晚于等于',
    date_before_or_equal: '早于等于',
    date_range: '日期范围',
    does_not_contains: '不包含',
    equal: '等于',
    exact_date: '指定日期',
    function_date_time_after: '晚于',
    function_date_time_before: '早于',
    is_empty: '为空',
    is_not_empty: '不为空',
    is_repeat: '有重复',
    name: '筛选',
    not_equal: '不等于',
    or: '或者',
    previous_month: '上月',
    previous_week: '上周',
    search: '搜索',
    settings_descritpion: '视图更改尚未保存，只会对你生效',
    settings_name: '筛选设置',
    some_day_after: '多少天后',
    some_day_before: '多少天前',
    start_end_date: '开始时间 - 结束时间',
    the_last_month: '过去 30 天',
    the_last_week: '过去 7 天',
    the_next_month: '未来 30 天',
    the_next_week: '未来 7 天',
    this_month: '本月',
    this_week: '本周',
    this_year: '今年',
    today: '今天',
    tomorrow: '明天',
    where: '当',
    yesterday: '昨天',
  },
  formula: {
    abs: {
      description:
        '简介\n取数值的绝对值。\n\n参数说明\nvalue：是要对其求绝对值的数值。\n绝对值：正数的绝对值是本身，负数的绝对值是去掉负号。',
      example:
        '// value > 0\n公式：ABS(1.5)\n运算结果：1.50\n\n//value = 0\n公式：ABS(0)\n运算结果：0.00\n\n// value < 0\n公式：ABS(-1.5)\n运算结果：1.50',
      name: 'Abs',
    },
    and: {
      description:
        '如果所有参数均为真（true），则返回真（true），否则返回假（false）。\n\n【logical】是逻辑参数，可以是逻辑值、数组或引用的字段',
      example: 'AND(3>2, 4>3)\n=> true',
      name: 'And',
    },
    array: '数组',
    array_compact: {
      description:
        '从数组中删除空字符串和空值。\n\n【item】表示数组值，比如多选、附件、关联和查找引用字段类型的单元格值。\n\n本函数会保留“ false”值和空白字符的字符串',
      example: 'ARRAYCOMPACT([1,2,"",3,false," ", null])\n=> [1,2,3,false," "]',
      name: 'Array Compact',
    },
    array_flatten: {
      description:
        '通过删除任何数组嵌套来平铺数组。 所有数据都成为同一个数组的元素。\n\n【item】表示数组值，比如多选、附件、关联和查找引用字段类型的单元格值。',
      example: 'ARRAYFLATTEN([1, 2, " ", 3, ],[false])\n=> [1, 2, 3 ,false]',
      name: 'Array Flatten',
    },
    array_join: {
      description:
        '将汇总数组中的所有值拼接成一个带分隔符的字符串。\n\n【item】表示数组值，比如多选、附件、关联和查找引用字段类型的单元格值。',
      example: 'ARRAYJOIN({兴趣爱好} , "; ")',
      name: 'Array Join',
    },
    array_unique: {
      description:
        '仅返回数组中的唯一项。\n\n【item】表示数组值，比如多选、附件、关联和查找引用字段类型的单元格值。',
      example: 'ARRAYUNIQUE([1,2,3,3,1])\n=> [1,2,3]',
      name: 'Array Unique',
    },
    average: {
      description:
        '返回多个数值的算术平均数。\n\n【number...】是进行运算的数值参数，可以输入数字或引用数值类型的列。数值类型的列包括数字、货币、百分比、评分等。\n\n如果其中某个参数是文本值，比如""八""，在运算时会被当做0。',
      example: 'AVERAGE(2, 4, "6", "八") => (2 + 4 + 6) / 4 = 3',
      name: 'Average',
    },
    blank: {
      description:
        '返回一个空值。\n\n可以用来判断单元格是否为空，见例子一；\n可以在单元格内填入空值，见例子二；',
      example:
        'IF({开始时间} = BLANK(), "尚未确定时间", "已确定开始时间")\n\nIF({数学成绩} ≥ 60, BLANK(), "需要补考")',
      name: 'Blank',
    },
    ceiling: {
      description:
        '将数值向上舍入为最接近的指定基数的倍数。\n\n【value】是要向上舍入的值。\n【significance】非必填，是用于向上舍入的基数，返回值为基数的倍数。如果未提供，默认取1。\n【向上舍入】即它返回值是大于或等于原数值,且为最接近的基数的倍数。',
      example: 'CEILING(1.99)\n=> 2\n\nCEILING(-1.99, 0.1)\n=> -1.9',
      name: 'Ceiling',
    },
    concatenate: {
      description:
        '将多个文本值串联成单个文本值。（其效果等同于 &）\n\n【text1..】是要串联的多个值，可以输入文本、数字、日期参数或者引用列数据。\n\n请用双引号将你要串联的文本值引起来，数字和引用列除外。\n特例：如果要串联双引号，你需要使用反斜杠（\\）作为转义字符。',
      example: 'CONCATENATE({姓名}, {年纪}, "岁")\n\nCONCATENATE("\\"", {年纪}, "\\"")',
      name: 'Concatenate',
    },
    count: {
      description:
        '统计「数字」类型值的数量。\n\n【number】可以是输入的参数或引用的列。\n\n本函数可以计算输入的参数或单元格内包含了多少个数值（数字、货币、百分比、评分都为数值）。',
      example: 'COUNT(1, 3, 5, "", "七")\n=> 3',
      name: 'Count',
    },
    count_a: {
      description:
        '统计非空值的数量。\n\n【textOrNumber】可以是输入的参数或引用的列。\n\n本函数可以计算输入的参数或单元格内包含了多少个非空值。\n比如，可以统计一个单元格内有多少个选项，多少个图片。多少个成员等。\n还可以统计查找引用的单元格内的数组非空值。',
      example: 'COUNTA(1, 3, 5, "", "七")\n=> 4',
      name: 'CountA',
    },
    count_all: {
      description:
        '统计所有值的数量，包括空值。\n\n【textOrNumber】可以是输入的参数或引用的列。\n\n本函数可以计算输入的参数和单元格内包含了多少个值，包括空值。',
      example: 'COUNTALL(1, 3, 5, "", "七")\n=> 5',
      name: 'CountAll',
    },
    count_if: {
      description:
        '在values中统计keyword出现的次数。\n\nvalues：指定从哪里查找数据。支持数组类型或文本类型的数据。\nkeyword：要查找并统计的关键词。\noperation：比较符，非必填项。你可以填入条件符号大于">"，小于"<"，等于"="，不等于"!="，如果不填写默认为等于。\n例子一中没有填写比较符，默认统计等于"A"的值出现的次数。\n例子二中填写了比较符">"，意味统计大于"2"的值出现的次数。\n\n使用场景：\n1）可以统计一串文本数组[A, B , C , D, A]中，字符"A"出现的数量为2，见例子一。\n2）可以统计一串数字数组[1, 2, 3, 4, 5]中，大于3的数字数量为2，见例子二。\n3)可以统计一串文本字符串"吃葡萄不吐葡萄皮"中，"葡萄"出现的次数为2，见例子三。',
      example:
        'COUNTIF({评级}, "A")\n=> 2\n\nCOUNTIF({得分}, 3, ">")\n=> 2\n\nCOUNTIF({顺口溜}, "葡萄")\n=> 2\n',
      name: 'CountIf',
    },
    created_time: {
      description: '返回记录的创建时间',
      example: 'CREATED_TIME()\n=> "2024-06-10"\n\n"创建于 ：" & CREATED_TIME()',
      name: 'Created Time',
    },
    date: '日期',
    date_add: {
      description:
        '简介\n为指定的日期增加固定的时间间隔。\n\n参数说明\ndate：是你指定的日期。本函数将在该日期基础上增加一定的时间间隔。\ncount：是时间间隔，支持输入带正负号的数字。如果为正数，即表示增加几天（可自定义计时单位），见例子一；如果为负数，即表示减少几天，见例子二；\nunits：是计时单位，即增加时间间隔的单位。比如按 “天” 计算也可以转换为按 “年” 计算。\n\n计时单位包括以下符号，两种格式都可以使用：「单位说明符 」→ 「缩写」\n毫秒：「milliseconds」 → 「ms」\n秒：「seconds」 → 「s」\n分钟：「minutes」 → 「m」\n小时：“hours” → “h”\n天：“days” → “d”\n周：“weeks” → “w”\n月：“months” → “M”\n季度：“quarters” → “Q”\n年：“years” → “y”\n\n点击下方链接可查看全部计时单位。',
      example:
        '// 为 2024/03/25 增加 1 天的时间间隔。计时单位 "days" 表示按 “天” 计算。\n公式：DATEADD("2024/03/25", 1, "days")\n运算结果：2024/03/26\n\n// 为 2024/03/25 减少 1 天的时间间隔。计时单位 "days" 表示按 “天” 计算。\n公式：DATEADD("2024/03/25", -1, "days")\n运算结果：2024/03/24\n\n// 为 {启动时间} 增加 10 天的时间间隔。下列字段 {启动时间} 是日期类型且单元格值 2024/03/25 。计时单位 "days" 表示按 “天” 计算。\n公式：DATEADD({启动时间}, 10, "days")\n运算结果：2024/04/04',
      name: 'Date Add',
    },
    datestr: {
      description:
        '将日期格式化为“年-月-日”形式的文本（固定格式为YYYY-MM-DD）\n\n【date】是被格式化的日期\n\n日期经过格式化后，将变为一串文本，不再具有日期数据的属性。',
      example: 'DATESTR("2024/10/01")\n=> 2024-10-01\n\nDATESTR({启动时间})\n=> 2024-06-10',
      name: 'Datestr',
    },
    datetime_diff: {
      description:
        '返回两个日期之间的差值（有正负），即日期1减去日期2。\n\n【date1】日期1\n【date2】日期2\n【units】计时单位，日期1与日期2差值的计算单位。比如按“天”计算也可以转换为按“年”计算。\n\n计时单位包括以下符号，两种格式都可以使用：「单位说明符 」→ 「缩写」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分钟："minutes" → "m"\n小时："hours" → "h"\n天："days" → "d"\n周："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n\n点击下方链接可查看全部计时单位。',
      example:
        'DATETIME_DIFF( "2024-08-11"  ,"2024-08-10", "days")\n=> 1\n\n\nDATETIME_DIFF( "2024-08-9" ,"2024-08-10", "days")\n=> -1\n\nDATETIME_DIFF( {截止时间} , TODAY(), "hours")\n=> 48',
      name: 'Datetime Diff',
    },
    datetime_format: {
      description:
        '将日期以自定义的形式格式化为文本。\n\n【date】是需要被格式化的日期。\n【output_specifier】是选择的格式化说明符。比如说明符可以是：\n"DD-MM-YYYY"表示"日-月-年"，见例子一；\n" YYYY / MM / DD"表示"年/月/日"，见例子二；\n"MM.DD"表示"月.日"，见例子三。\n\n日期经过格式化后，将变为一串文本。\n\n本函数支持的日期格式化说明符请看下方链接。',
      example:
        'DATETIME_FORMAT("2024-10-01", "DD-MM-YYYY")\n=> 01-10-2024\n\nDATETIME_FORMAT("2024-10-01", "YYYY / MM / DD")\n=>2024/10/01\n\nDATETIME_FORMAT("2024-10-01", "MM.DD")\n=>10.01\n\nDATETIME_FORMAT(TODAY(), "DD-MM-YYYY")\n=> 01-10-2024',
      name: 'Datetime Format',
    },
    datetime_parse: {
      description:
        '将文本转换为结构化日期类型。\n\n【date】是要被格式化为日期的文本。\n【input_format】非必填，本参数是日期格式化说明符。对于系统无法识别的文本日期内容，你可以自己解释为结构化的日期。见例子二。\n\n本函数支持的日期格式化说明符和语言环境请查看下方链接。',
      example:
        'DATETIME_PARSE("20241001")\n=> "2024/10/01"\n\nDATETIME_PARSE("01 10 2024 18:00", "DD MM YYYY HH:mm")\n=> "2024/10/01 18:00"\n\nDATETIME_PARSE("01号10月2024年18:00时", "DD号MM月YYYY年HH:mm时")\n=> 2024/10/01 18:00',
      name: 'Datetime Parse',
    },
    day: {
      description:
        '返回指定日期属于当月的第几号，输出格式为1-31之间的整数。\n\n【date】是指定的日期。\n比如，数字1表示日期属于当月的第1号。',
      example: 'DAY("2024.10.01")\n=>1\n\nDAY({完成日期})\n=>5',
      name: 'Day',
    },
    encode_url_component: {
      description:
        '把文本编码为 URL的格式。\n\n【component_string】是需要被编码的文本。不编码以下字符：- _ . ~\n\n比如将第一个例子的输出值复制到浏览器地址栏，就相当于在百度里搜索”苹果“的URL',
      example: 'https://www.baidu.com/s?wd=" & ENCODE_URL_COMPONENT（{搜索关键词}）',
      name: 'Encode URL Component',
    },
    error: {
      description:
        '在单元格内显示错误提示和原因。\n\n可以在函数内输入文本说明错误原因，比如例子中的”统计错误“就是你定义的错误原因。',
      example: 'IF({年纪}< 0, ERROR("统计错误"), "正常")\n=>  #Error: 统计错误',
      name: 'Error',
    },
    even: {
      description:
        '返回沿绝对值增大方向最接近的偶数。\n\n【value】是要取偶的数值。\n【绝对值增大】即它返回值是远离0（零）方向。',
      example: 'EVEN(1.5)\n=> 2\n\nEVEN(-1.8)\n=> -2',
      name: 'Even',
    },
    example: '示例',
    exp: {
      description:
        '返回e的指定次方。\n\n【e】是自然数，约为2.718282\n【power】是幂。即指定e的多少次方。',
      example: 'EXP(1)\n=> 2.72\n\nEXP(2)\n=> 7.40',
      name: 'Exp',
    },
    false: {
      description:
        '返回逻辑值false\n\n可以判断勾选类型的字段中单元格是否为”未选中状态“，见例子一；\n\n可以和FALSE一起使用输出为真和假的布尔值，见例子二；',
      example:
        'IF({完成状态(勾选)}= FALSE(), "未完成"， "已完成")\n\nIF({平均成绩} >60, TRUE(), FALSE())',
      name: 'False',
    },
    features_list: '公式列表',
    find: {
      description:
        '查找特定的文本在内容中第一次出现的位置。\n\n【stringToFind】是要查找到的特定文本。\n【whereToSearch】指定从哪段内容内查找文本。可以输入文本参数或者引用字段。\n【startFromPosition】非必填，指定从内容的哪个位置开始查找（用数字表示第几个字符）。\n\n本函数可以在一大段内容中快速查找特定文本出现的位置。\n如果返回数字3，表示文本出现在该内容的第3个字符。\n如果未找到匹配的文本，则结果将为0。\n\n其效果与SEARCH()类似，但是未找到匹配项时，SEARCH()返回值为空而不是0。',
      example:
        'FIND("苹果", "这个苹果又大又圆，要买两斤苹果吗？")\n=> 3\n\nFIND("香蕉", "这个苹果又大又圆，要买两斤苹果吗？")\n=> 0\n\nFIND("苹果", "这个苹果又大又圆，买两斤苹果吗？"，10)\n=> 13',
      name: 'Find',
    },
    floor: {
      description:
        '将数值向下舍入为最接近的指定基数的倍数。\n\n【value】是要向下舍入的值。\n【significance】非必填，是用于向下舍入的基数，返回值为基数的倍数。如果未提供，默认取1。\n【向下舍入】即它返回值是小于或等于原数值,且为最接近基数的倍数。',
      example: 'FLOOR(1.01, 0.1)\n=> 1.0\n\nFLOOR(-1.99, 0.1)\n=> -2.0',
      name: 'Floor',
    },
    from_now: {
      description:
        '返回当前日期和指定日期之间的差值（无正负）。\n\n【date】是指定日期，即用指定日期减去当前日期，计算两个日期相差的多少天（自定义计时单位），无正负。\n【units】计时单位，即指定日期与当前日期差值的计算单位，比如按”天“计算也可以转换为按”年“计算。\n\n计时单位包括以下符号，两种格式都可以使用：\n「单位说明符 」→ 「缩写」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分钟："minutes" → "m"\n小时："hours" → "h"\n天："days" → "d"\n周："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n点击下方链接可查看全部计时单位。',
      example: 'FRONOW("2023-08-10", "y")\n=> 1\n\nFROMNOW({开始日期}, "days")\n=> 25',
      name: 'From Now',
    },
    hour: {
      description:
        '返回指定日期的对应的时刻，输出格式为0（12:00 am）到23（11:00 pm）之间的整数。\n\n【date】是指定的日期。\n比如，18表示18:00',
      example: 'HOUR({打卡时间})\n=> 9',
      name: 'Hour',
    },
    if: {
      description:
        '判断是否满足某个条件，如果满足则返回第一个值，如果不满足则返回第二个值。\n\n【logical】是逻辑条件，表示计算结果为真（true）和假（false）的表达式。\n【value1】是当逻辑条件为真时的返回值。\n【value2】是当逻辑条件为假时的返回值。\n\nIF支持嵌套使用，并且可以用于检查单元格是否为空白/为空。',
      example:
        'IF({分数} > 60, "及格", "不及格")\n\nIF({水温} >  40, IF({水温} < 60, "刚刚好", "太热"), "太冷")\n\nIF({Date} = BLANK(), "请输入日期", "日期已经输入")',
      name: 'If',
    },
    input_formula: '输入公式',
    int: {
      description:
        '将数值向下舍入为最接近的整数。\n\n【value】是要向下舍入的值。\n【向下舍入】即它返回值是小于或等于原数值。',
      example: 'INT(1.99)\n=> 1\n\nINT(-1.99)\n=> -2',
      name: 'Int',
    },
    is_after: {
      description:
        '比较日期1是否晚于日期2，如果晚于则返回真（true），否则返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n\n日期可以是输入的参数，见用例一；\n日期也可以是引用日期类型的字段，见用例二。\n\n在单元格内真和假用"已勾选"和"未勾选"表示。',
      example:
        'IS_AFTER("2024-10-02" , "2024-10-01")\n=> TRUE\n\nIS_AFTER({截止时间}, TODAY())\n=> TRUE\n\nIS_AFTER({截止时间}, "2024-10-01")\n=> TRUE',
      name: 'Is After',
    },
    is_before: {
      description:
        '比较日期1是否早于日期2，如果早于则返回真（true），否则返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n\n日期可以是输入的参数，见用例一；\n日期也可以是引用日期类型的字段，见用例二。\n在单元格内真和假用"已勾选"和"未勾选"表示。',
      example:
        'IS_BEFORE("2024-10-01" , "2024-10-02")\n=> TRUE\n\nIS_BEFORE({截止时间}, TODAY())\n=> TRUE\n\nIS_BEFORE({截止时间}, "2024-10-01")\n=> TRUE',
      name: 'Is Before',
    },
    is_error: {
      description:
        '检查一个公式运行是否错误，如果错误则返回真（true）。\n\n【expr】是需要检查的值。检测值可以是算术运算、逻辑判断等类型的公式。',
      example: 'IS_ERROR(2/0)\n=> TRUE\n\nIS_ERROR("哈哈"*2)\n=> TRUE',
      name: 'Is Error',
    },
    is_same: {
      description:
        '确定日期1是否等于日期2，如果等于则返回真（true），否则返回假（false）。\n\n【date1】是日期1。\n【date2】是日期2。\n【units】非必填， 是比较的时间单位。比如比较两个日期是否相等，一直对比到分钟单位。\n\n日期可以是输入的参数，见用例一；\n日期也可以是引用日期类型的字段，见用例四。\n在单元格内真和假用"已勾选"和"未勾选"表示。\n\n点击下方链接可查看全部计时单位。',
      example:
        'IS_SAME("2024-10-01" , "2024-10-01")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "years")\n=> TRUE\n\nIS_SAME("2024-10-01" , "2024-11-11", "months")\n=> FALSE\n\nIS_SAME({截止时间}, {完工时间}, "days")\n=> TRUE\n\nIS_SAME("2024-10-01", {完工时间}, "days")\n=> TRUE',
      name: 'Is Same',
    },
    last_modified_time: {
      description:
        '返回每一行的单元格中进行最后一次修改的时间 。\n注意：系统只会返回计算类型列的单元格的修改。\n\n如果你只关心特定字段的单元格的更新时间，你可以指定一个或多个列，见例子二和三。',
      example:
        'LAST_MODIFIED_TIME()\n=> "2024-06-10 6:27 下午."\n\nLAST_MODIFIED_TIME({项目进度})\n=> "2024-06-09 1:27 上午"\n\nLAST_MODIFIED_TIME({项目进度}, {任务分配})\n=> 2024-06-09 1:27 上午',
      name: 'Last Modified Time',
    },
    left: {
      description:
        '从文本的开头提取多个字符。\n\n【string】是要被提取字符的文本。\n【howMany】是提取的字符数量。用数字表示，比如"4"，代表从左到右提取4个字符。',
      example: 'LEFT("Bika：支持API，随意DIY", 4)\n=> Bika\n\nLEFT({出生年月}, 4)\n=> 1994',
      name: 'Left',
    },
    len: {
      description:
        '统计一段文本的字符长度。\n\n【string】是要计算长度的文本；标点符号、空格等也会占一个字符。',
      example: 'LEN("你猜猜我有多长？")\n=> 8\n\nLEN("a blank")\n=> 7',
      name: 'Len',
    },
    log: {
      description:
        '以指定基数为底，返回数值的对数。\n\n【number】是想要计算其对数的数值。\n【base】是对数的基数（底数），如果未指定基数，则默认为10。',
      example: 'LOG(1024, 2)\n=> 10\n\nLOG(10000)\n=> 4',
      name: 'Log',
    },
    logic: '逻辑',
    lower: {
      description: '将文本中所有大写字符全部转换为小写字符。\n\n【string】是被转换的文本。',
      example: 'LOWER("HELLO") => "hello"',
      name: 'Lower',
    },
    max: {
      description:
        '返回最大的数值。\n\n【number...】是进行运算的数值参数，可以输入数字或引用数值类型的列。数值类型的列包括数字、货币、百分比、评分等。\n\n另外，本函数的输入值都为日期格式时，可以比较多个日期中最晚的日期。',
      example: 'MAX(1, 3, 5, 7) => 7',
      name: 'Max',
    },
    mid: {
      description:
        '从内容中特定位置提取一段固定长度的文本。\n\n【string】是你输入的一段内容，其中包含了被提取的文本。该内容可以是输入的文本或者引用的字段数据。\n【whereToSearch】是你指定从哪儿提取文本，用数字表示。比如数字"3"表示从内容的第3个字符开始提取。\n【count】是提取的文本长度，用数字表示。比如数字"2"表示从指定位置提取2个字符。',
      example:
        'MID("这个苹果又大又圆", 3, 2)\n=> 苹果\n\nMID("这个苹果又大又圆", 99, 2)\n=> 空值\n\nMID("这个苹果又大又圆", 3, 99)\n=> 苹果又大又圆\n\nMID({嘉宾姓名}, 2, 99)\n=> 彦祖',
      name: 'Mid',
    },
    min: {
      description:
        '返回最小的数值。\n\n【number…】是进行运算的数值参数，可以输入数字或引用数值类型的列。数值类型的列包括数字、货币、百分比、评分等。\n\n另外，本函数的输入值都为日期格式时，可以比较多个日期中最晚的日期。',
      example: 'MIN({数学成绩}, {英语成绩}, {语文成绩}) => 80',
      name: 'Min',
    },
    minute: {
      description: '返回指定日期的分钟数，输出格式为0到59之间的整数。\n\n【date】是指定的日期。',
      example: 'MINUTE({打卡时间})\n=>30',
      name: 'Minute',
    },
    mod: {
      description:
        '返回两数值相除的余数。\n\n【value】是被除数。\n【divisor】是除数。\n\n返回结果的符号与除数的符号相同。',
      example: 'MOD(10, 3) => 1',
      name: 'Mod',
    },
    month: {
      description:
        '返回指定日期对应的月份。\n\n【date】是指定的日期。\n\n本函数输出值为1（一月）至12（十二月）之间的整数。',
      example: 'MONTH("2024.10.01")\n=> 10\n\nMONTH({毕业时间})\n=> 6',
      name: 'Month',
    },
    not: {
      description:
        '反转其参数的逻辑值。\n\n【boolean】是布尔参数，意味着你的输入值必须是逻辑判断且输出值只有真和假，比如比较两个值谁大谁小。\n当你参数的逻辑判断为真（true）时函数返回假（false）；\n当你参数的逻辑判断为假（false）时函数返回真（true）；\n\n如例子一：2>3输出值是假，但经过反转后函数输出值的是真。\n如例子二：NOT({年纪} > 18)经过NOT函数反转后，其实相当于判断{年纪} ≤ 18',
      example: 'NOT({年纪} > 18)',
      name: 'Not',
    },
    now: {
      description:
        '返回今天的日期和时间，会精确到时分秒。\n\n可以直接使用此函数返回年月日，见例子一；\n\n也可以和DATEADD或DATETIME_DIFF等函数一起使用，比如用{截止时间}减去当前时间，来显示项目的倒计时，见例子二。\n\n 注意：仅当重新刷新计算公式或刷新表格时，这个函数返回的结果才会更新。',
      example: 'NOW()\n=> "2024/06/02 07:12"\n\nDATETIME_DIFF( {截止时间} , NOW(),"days")\n=> 15',
      name: 'Now',
    },
    number: '数字',
    object: '对象',
    odd: {
      description:
        '返回沿绝对值增大方向最接近的奇数。\n\n【value】是要取奇的数值。\n【绝对值增大】即它返回值是远离0（零）方向。',
      example: 'ODD(1.5)\n=> 3\n\nODD(-2.1)\n=> -3',
      name: 'Odd',
    },
    or: {
      description:
        '如果任何一个参数为真（true），则返回真（true），否则返回假（false）。\n\n【logical】是逻辑参数，可以是逻辑值、数组或引用的字段。',
      example: 'OR(3>2, 2>3)\n=>  true',
      name: 'Or',
    },
    power: {
      description: '返回指定基数的幂。即指定基数的多少次方。\n\n【base】是基数。\n【power】是幂',
      example: 'POWER(2, 5)\n=> 32\n\nPOWER(-5, 3)\n=> -125',
      name: 'Power',
    },
    record_id: {
      description: '返回记录的ID',
      example: 'https://awesomeservice.com/view?recordId=" & RECORD_ID()',
      name: 'Record ID',
    },
    replace: {
      description:
        '将内容中特定位置的一段文本替换为新文本。\n\n【string】是你输入的一段内容，其中包含了被替换的文本。该内容可以是输入的文本或者引用的字段数据。\n【start_character】是你指定从哪儿替换文本，用数字表示。比如数字"3"表示从内容的第3个字符开始替换。\n【number_of_characters】是你指定要替换掉多少个字符，用数字表示。比如数字"2"表示替换掉指定位置的2个字符。\n【replacement】是替换原文本的新文本。\n\n（如果你想将内容中所有出现的原文本替换为新文本，请参见SUBSTITUTE。）',
      example:
        'REPLACE("这个苹果又大又圆", 3, 2, "桃子")\n=> 这个桃子又大又圆\n\nREPLACE("这个苹果又大又圆", 3, 99, "榴莲又香又甜")\n=> 这个榴莲又香又甜\n\nREPLACE({嘉宾姓名}, 1, 1, "X")\n=> X彦祖',
      name: 'Replace',
    },
    rept: {
      description:
        '根据指定次数重复文本。\n\n【string】是需要重复的文本。\n【mumber】是指定的重复次数。用数字表示，比如”2“，表示重复2次。',
      example: 'REPT("哈", 2)\n=> 哈哈',
      name: 'Rept',
    },
    right: {
      description:
        '从文本的末尾提取出多个字符。\n\n【string】是要被提取字符的文本。\n【howMany】是提取的字符数量。用数字表示，比如"5"，代表从右到左提取5个字符。',
      example: 'RIGHT("Bika：支持API，随意DIY", 5)\n=> 随意DIY\n\nRIGHT({出生年月}, 5)\n=> 07-13',
      name: 'Right',
    },
    round: {
      description:
        '按指定的位数对数值进行四舍五入。\n\n【value】是要四舍五入的值\n【precision】非必填，要进行四舍五入运算的位数。未填写时默认为1。\n\n如果位数大于 0，则四舍五入到指定的小数位。\n如果位数等于 0，则四舍五入到最接近的整数。\n如果位数小于 0，则在小数点左侧进行四舍五入。',
      example: 'ROUND(3.14159, 2) => 3.14',
      name: 'Round',
    },
    rounddown: {
      description:
        '按指定的位数将数值延绝对值减小方向舍入。\n\n【value】是要舍入的值。\n【precision】非必填，要将数字舍入到的位数。未填写时默认为1。\n【绝对值减小】即它返回值是靠近0（零）方向。\n\n如果位数大于 0，则四舍五入到指定的小数位。\n如果位数等于 0，则四舍五入到最接近的整数。\n如果位数小于 0，则在小数点左侧进行四舍五入。',
      example: 'ROUNDDOWN(3.14159, 2) => 3.14',
      name: 'Round Down',
    },
    roundup: {
      description:
        '按指定的位数将数值延绝对值增大方向舍入。\n\n【value】是要舍入的值。\n【precision】非必填，要将数字舍入到的位数。未填写时默认为1。\n【绝对值增大】即它返回值是远离0（零）方向。\n\n如果位数大于 0，则四舍五入到指定的小数位。\n如果位数等于 0，则四舍五入到最接近的整数。\n如果位数小于 0，则在小数点左侧进行四舍五入。',
      example: 'ROUNDUP(3.14159, 2) => 3.15',
      name: 'Round Up',
    },
    search: {
      description:
        '搜索特定的文本在内容中第一次出现的位置。\n\n【stringToFind】是要搜索到的特定文本。\n【whereToSearch】指定从哪段内容搜索文本。可以输入文本参数或者引用字段。\n【startFromPosition】非必填，指定从内容的哪个位置开始搜索（用数字表示第几个字符）。\n\n本函数可以在一大段内容中快速搜索特定文本出现的位置。\n如果返回数字3，表示文本出现在该内容的第3个字符。\n如果未找到匹配的文本，则结果将为空值。\n\n其效果与FIND()类似，但是未找到匹配项时，FIND()返回值为0而不是空值。',
      example:
        'SEARCH("苹果", "这个苹果又大又圆，要买两斤苹果吗？")\n=> 3\n\nSEARCH("香蕉", "这个苹果又大又圆，要买两斤苹果吗？")\n=> 空值\n\nSEARCH("苹果", "这个苹果又大又圆，买两斤苹果吗？"，10)\n=> 13',
      name: 'Search',
    },
    second: {
      description: '返回指定日期的秒种，输出格式为0到59之间的整数。\n\n【date】是指定的日期。',
      example: 'SECOND({打卡时间})\n=> 1',
      name: 'Second',
    },
    select_a_formula: '选择字段或函数',
    set_locale: {
      description:
        '为指定日期时间设置特定的语言环境。\n\n【date】是指定日期。\n【ocale_modifier】是语言环境说明符。\n\n本函数必须与DATETIME_FORMAT结合使用。可以点击下方链接查看支持的语言环境说明符。',
      example: 'DATETIME_FORMAT(SET_LOCALE(NOW(), "zh-cn"), "lll")\n=> 2024年6月2日上午11点04分',
      name: 'Set Locale',
    },
    set_timezone: {
      description:
        '为指定日期设置特定的时区。\n\n【date】是指定日期。\n【tz_identifier】是时区说明符。比如"8"代表东8区，"-2"代表西2区。\n\n本函数必须与DATETIME_FORMAT结合使用。',
      example: 'DATETIME_FORMAT(SET_TIMEZONE(NOW(), -8), "M/D/YYYY h:mm")\n=> 9/20/2024 2:30',
      name: 'Set Timezone',
    },
    sqrt: {
      description:
        '返回数值的平方根。\n\n【value】是要对其求平方根的数值。\n\n如果数值为负数，则 SQRT 返回 Nan',
      example: 'SQRT(16) => 4',
      name: 'Sqrt',
    },
    substitute: {
      description: 'function_substitute_summary',
      example:
        'SUBSTITUTE("小胡，小张，小王", "小", "老")\n=> 老胡，老张，老王\n\nSUBSTITUTE("小胡，小张，小王", "小", "老", 3)\n=> 小胡，老张，小王',
      name: 'Substitute',
    },
    sum: {
      description:
        '将所有数值相加。\n      【number...】是进行运算的数值参数，可以输入数字或引用数值类型的列。\n      数值类型的列包括数字、货币、百分比、评分等',
      example: 'SUM(1, 3, 5, "", "VI") => 1 + 3 + 5 = 9',
      name: 'Sum',
    },
    switch: {
      description:
        '本函数为多分支选择函数，它由表达式和多个分支+返回值组成，如果表达式等于某个分支值，则函数输出该分支对应的返回值。\n\n【expression】是表达式，其运算的结果会与每个分支进行匹配。\n【pattern】是分支，每个分支代表表达式的可能运算结果。每一个分支都有对应的返回值。\n【result】是返回值，如果表达式的运算结果匹配了一个分支，则输出对应的返回值。\n【default】是默认值，如果运算结果没有匹配任何一个分支，则函数输出默认值。默认值未填写时为空值。\n\n比如例子一，{国家}是引用的一列数据，其输出值可能是成千上万个国家名称，它是该函数中的表达式。“中国”和“中文”分别为其中一条分支和返回值，它表示如果{国家}的输出值为“中国”时，则返回“中文”。而“通用英语”为默认值，它表示{国家}的输出值没有匹配任何分支时，则输出“通用英语”',
      example:
        'SWITCH({国家}, "中国", "中文", "俄国", "俄语", "法国", "法语", "日本", "日语", "通用英语")\n\nSWITCH("C", "A", "优秀", "B", "中等", "C", "普通", "D", "较差", "没有成绩")\n=>普通',
      name: 'Switch',
    },
    t: {
      description:
        '如果输入值为文本类型，则返回原文本，如果非文本类型则返回空值。\n\n【value】是被检查是否为文本的值。\n\n比如，输入值引用数字、日期等类型的字段，那么将会返回空值。',
      example:
        'T("Bika")\n=> Bika\n\nT("55")\n=> 55\n\nT(55)\n=> 空值\n\nT({数学成绩})\n=> 空值    ',
      name: 'T',
    },
    text: '文本',
    timestr: {
      description:
        '将日期格式化为"时:分:秒"形式的文本(固定格式为HH:mm:ss)\n\n【date】是被格式化的日期\n\n日期经过格式化后，将变为一串文本，不再具有日期数据的属性。',
      example: 'TIMESTR(NOW())\n=> 04:52:12',
      name: 'Timestr',
    },
    to_now: {
      description:
        '返回当前日期和指定日期之间的差值（无正负）。\n\n【date】是指定日期，即用指定日期减去当前日期，计算两个日期相差的多少天（自定义计时单位），无正负。\n【units】计时单位，即指定日期与当前日期差值的计算单位，比如按”天“计算也可以转换为按”年“计算。\n\n计时单位包括以下符号，两种格式都可以使用：\n「单位说明符 」→ 「缩写」\n毫秒："milliseconds" → "ms"\n秒："seconds" → "s"\n分钟："minutes" → "m"\n小时："hours" → "h"\n天："days" → "d"\n周："weeks" → "w"\n月："months" → "M"\n季度："quarters" → "Q"\n年："years" → "y"\n点击下方链接可查看全部计时单位。',
      example: 'TONOW("2023-08-10", "y")\n=> 1\n\nTONOW({开始日期}, "days")\n=> 25',
      name: 'To Now',
    },
    today: {
      description:
        '返回今天的日期（年月日），但不会精确到时分秒（默认为00:00:00）。如果想要精确到时分秒，请使用函数NOW。\n\n可以直接使用此函数返回年月日，见例子一；\n也可以和DATEADD或DATETIME_DIFF等函数一起使用，比如用{截止时间}减去当前时间，来显示项目的倒计时，见例子二。\n\n注意：仅当重新刷新计算公式或刷新表格时，这个函数返回的结果才会更新。',
      example: 'TODAY() => 2024/06/02',
      name: 'Today',
    },
    trim: {
      description: '清除文本开头和结尾的空格。\n\n【value】是需要被处理的文本。',
      example: 'TRIM(" 两边空格会被清除! ")\n=>两边空格会被清除!',
      name: 'Trim',
    },
    true: {
      description:
        '【简介】\n返回逻辑值真（ true ）。\n\n【参数说明】\n该函数不需要填写参数。\n该函数可以判断勾选类型的字段是否为 “已选中状态” ，见例子一；\n该函数和 FALSE() 一起使用可以输出为真和假的布尔值，见例子二。',
      example:
        '// 判断勾选类型的字段的状态。例如下列 {是否完成} 字段为勾选类型且单元格值为 "已勾选"时：\n公式：IF({是否完成} = TRUE(), "已完成"， "未完成")\n运算结果："已完成"\n\n// TRUE() 和 FALSE() 一起使用输出布尔值。例如下列 {成绩} 字段为数字类型且单元格值为 70 时：\n公式：IF({成绩} > 60, TRUE(), FALSE())\n运算结果：true',
      name: 'True',
    },
    upper: {
      description: '将文本中所有小写字符全部转换为大写字符。\n\n【string】是被转换的文本。',
      example: 'UPPER("hello") => "HELLO"',
      name: 'Upper',
    },
    usage: '使用方法',
    value: {
      description:
        '将文本字符串转换为数字。\n\n【text】表示要转换的文本值。\n\n本函数可以将文本内的数字提取出来。',
      example: 'VALUE("$10000")\n=> 10000',
      name: 'Value',
    },
    weekday: {
      description:
        '返回指定日期对应一周中的星期几。\n\n【date】是指定的日期。\n【startDayOfWeek】非必填，是一周的开始时间，默认情况下每周从星期日开始（即周日为0）。 你还可以将开始时间设置为"Monday"(星期一，见例子二)\n\n本函数输出值为0到6之间的整数。 ',
      example:
        'WEEKDAY("2024.10.01")\n=>4\n\nWEEKDAY("2024.10.01", "Monday")\n=>3\n\nWEEKDAY(TODAY())',
      name: 'Weekday',
    },
    weeknum: {
      description:
        '返回指定日期对应为一年中的第几个星期。\n\n【date】是指定的日期。\n【startDayOfWeek】非必填，是一周的开始时间，默认情况下每周从星期日开始（即周日为0）。 你还可以将开始时间设置为"Monday"(星期一)\n\n本函数输出值为整数。比如6，代表该日期属于一年中的第6个星期。',
      example:
        'WEEKNUM("2024.10.01")\n=>40\n\n\nWEEKNUM("2024.10.01", "Sunday")\n=>40\n\nWEEKNUM(TODAY())\n=>33',
      name: 'Weeknum',
    },
    workday: {
      description:
        '返回起始日期若干个工作日之后的日期。\n\n【startDate】是你指定的起始日期。\n【numDays】是你指定的起始日期之后的工作日天数，用正数表示。比如，数字“1”代表起始日期一个工作日之后的日期，见例子一；\n【holidays】非必填。是要从日历中去除的特定日期，例如节假日。其输入格式为「yyyy-mm-dd」，多个日期以逗号分隔的，见例子三。\n\n本函数的工作日不包括周末和你指定的特定日期。',
      example:
        'WORKDAY("2024/10/01" , 1)\n=> 2024/10/02\n\nWORKDAY("2024/10/01" , 1，"2024-10-02")\n=> 2024/10/05\n\nWORKDAY({启动日期}, 100, "2024-10-01, 2024-10-02, 2024-10-03, 2024-10-04, 2024-10-05, 2024-10-06, 2024-10-07, 2024-10-08")\n=> 2024-11-11',
      name: 'Workday',
    },
    workday_diff: {
      description:
        '统计两个日期之间相隔多少个工作日（有正负）。\n\n【startDate】起始日期。\n【endDate】截止日期。如果起始日期比截止日期晚，则会出现负数。\n【holidays】非必填。是要从工作日历中去除的日期，例如节假日。其输入格式为「yyyy-mm-dd」，多个日期以逗号分隔的。\n\n本函数统计起止日期之间的工作日，不包括周末和你指定的特定日期。',
      example:
        'WORKDAY_DIFF("2024-10-01", "2024-10-02")\n=> 2\n\nWORKDAY_DIFF("2024-10-02", "2024-10-01")\n=> -2\n\nWORKDAY_DIFF("2024-10-01", "2024-10-05")\n=> 3\n\nWORKDAY_DIFF({产品启动日期}, {产品上线日期} , "2024-06-25, 2024-06-26, 2024-06-27")\n=> 100',
      name: 'Workday Diff',
    },
    xor: {
      description:
        '如果奇数个参数为真（true），则返回真（true），否则返回假（false）。\n\n【logical】是逻辑参数，可以是逻辑值、数组或引用的字段。',
      example: 'XOR(3>2, 2>3, 4>3)\n=> false',
      name: 'Xor',
    },
    year: {
      description: '返回指定日期对应的四位数年份。\n\n【date】是指定的日期。',
      example: 'YEAR("2024/10/01")\n=> 2024\n\nYEAR({毕业时间})\n=> 2024',
      name: 'Year',
    },
  },
  global: {
    action: {
      cannot_be_empty: '不能为空',
      detail: '详情',
      full_screen: '全屏',
      no_result_found: '暂无搜索记录',
      preview: '预览',
      select: '选择',
      toggle: '切换',
      un_named: '未命名',
      zoom_in: '放大',
      zoom_out: '缩小',
    },
    copilot: {
      delete_history: '删除对话',
      delete_history_confirm: '确定要删除这个对话历史吗？',
      history: '对话历史',
      history_empty: '暂无对话历史',
      history_loading: '加载中...',
      history_no_description: '无描述',
      history_no_more: '没有更多了',
      history_no_title: '无标题',
      new_chat: '新建对话',
      node_resource: '节点资源',
      title: 'AI 助手',
      upload_file: '上传文件',
      welcome: '你好，我是你的 AI 助手，随时为你提供帮助！',
    },
    dl_link_unavailable: '下载链接不可用',
    download: '下载',
    error_description: '我们的技术团队已经被告知并正在努力解决这个问题。请您尝试以下操作：',
    error_reason: [
      '返回上一页并刷新页面，然后再次尝试。',
      '暂时离开并稍后再试。',
      '如果问题持续存在，请联系客户支持团队以获取进一步的帮助。',
    ],
    guest: '访客',
    guest_management: '访客管理',
    hooks: {
      firebase: {
        create_hardware_description: '硬件设备集成绑定',
        create_hardware_name: '硬件设备绑定',
      },
    },
    me: '我(当前访问者)',
    page_not_found: '抱歉，您访问的页面未找到',
    page_not_found_description: '我们无法找到您请求的页面。可能由于以下原因：',
    page_not_found_reason: [
      '您输入的URL有误或拼写错误',
      '所请求的页面已被删除或移动',
      '我们的服务器暂时无法找到所请求的资源',
    ],
    retry: '重试',
    select: '请选择',
    select_path: '请选择完整路径',
    server_error: '抱歉，服务器在处理您的请求时遇到了问题',
    toast: {
      description_update_success: '描述修改成功',
      open_in_database: '请在表格页面打开',
    },
    welcome: '欢迎来到 {name}',
  },
  grid: {
    asc_option: '选项正序',
    batch_update_selected_record: '批量更新 {recordCount} 条记录',
    bulk_update: '批量更新',
    bulk_update_confirm_content: '将有 {count} 条记录会被修改，确定要保存吗？',
    bulk_update_confirm_title: '确认批量更新',
    bulk_update_fields: '需修改的字段',
    bulk_update_successful: '批量编辑记录成功',
    bulk_update_title: '批量更新记录',
    copy_row: '复制行',
    created_doc: '创建文档',
    delete_n_record: '删除{count} 条记录',
    desc_option: '选项倒序',
    duplicate_record: '复制记录',
    edit_record: '编辑记录',
    filter_to_find_all_records: '筛选查找数据库内所有需批量编辑的记录',
    group: '组',
    lookup_unamed_record: '未命名记录',
    new_record: '新建记录',
    pasting_multiple_columns_is_not_supportted_currently: '暂不支持粘贴多列数据',
    record_selected: '已选择{count}条记录',
    row_group: '分组',
    see_more_detail: '查看更多',
    select_record_by_name: '选择 {name}',
    tooltip_new_record: '点击新建一行记录',
    un_named_doc: '未命名文档',
  },
  help: {
    description: '想了解更多或仍然需要帮助？',
    help: '帮助',
    help_and_support: '帮助与支持',
    help_center: '帮助中心',
    title: '我们能帮到你什么？',
  },
  integration: {
    advertise: {
      can_do: '以下是一些富有启发性的应用场景示例，供您参考:',
      connect_to: '连接到 {name}',
      notice: '注意：此应用将对您账户中的所有用户开放。安装此应用即表示您同意其服务条款。',
    },
    airtable: {
      airtable_token: 'Airtable API Token',
      airtable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        '让用户能够将 Airtable 的表单数据直接同步到系统中。通过自动化流程，可以在表单数据更新时自动触发相关操作，如更新数据库、发送通知或生成报表。适用于需要实时同步和管理数据的业务场景',
      title: 'Airtable',
    },
    aitable: {
      aitable_token: 'AITable API Token',
      aitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        '集成使得用户可以将 AITable 的数据集成到系统中。结合自动化流程，可以在数据发生变化时自动触发各种任务，如数据同步、通知推送或报告生成。适用于需要高效数据管理和实时响应的场景',
      title: 'AITable.ai',
    },
    apitable: {
      apitable_token: 'APITable API Token',
      apitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        '用户可以将 APITable 的数据与系统无缝连接。利用自动化流程，可以在数据变化时自动执行预设任务，如更新记录、发送提醒或触发其他操作。适用于需要灵活数据管理和快速响应的应用场景',
      title: 'APITable',
    },
    awsocr: {
      aws_ocr_token: 'AWS Textract API Token',
      aws_ocr_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AWS Textract 用于连接和管理 AWS 的光学字符识别服务，支持图像文字识别和提取，帮助用户高效处理和存储文本数据。',
      title: 'AWS Textract',
    },
    azure: {
      apikey: 'Azure AI API 密钥',
      apikey_placeholder: '请输入您的 Azure AI API 密钥',
      description:
        '使用 Azure AI（Azure OpenAI / Azure AI Agents）服务，通过 API 密钥调用模型，支持部署完成、聊天、嵌入等能力。',
      title: 'Azure AI',
    },
    banner_description: '集成中心提供了丰富的集成服务，帮助您更好地连接各种应用和服务。',
    banner_title: '集成应用',
    bedrock: {
      apikey: 'Amazon Bedrock API 密钥',
      apikey_placeholder: '请输入您的 Amazon Bedrock API 密钥',
      description:
        '通过 Amazon Bedrock 调用 Foundation 模型，实现强大的生成式 AI 能力，包括模型微调、知识库增强与任务代理。',
      title: 'Amazon Bedrock',
    },
    byte_doubao: {
      description: '字节豆包集成，提供智能对话和内容生成服务。',
      title: '字节豆包',
      use_cases: [
        '帮助学生解决学科问题和提供论文写作指导',
        '支持职场人士撰写报告和获取专业建议',
        '提供旅行规划、娱乐推荐和健康咨询等日常服务',
        '为创意工作者提供灵感启发和设计建议',
      ],
    },
    claudeai: {
      description:
        'Claude 系列模型是由 Anthropic 公司开发的大语言模型（包含 Haiku、Sonnet、Opus），能够提供智能对话、内容生成和数据分析等服务。它能够理解复杂的查询，并提供精确的回答，帮助用户提高工作效率和创造力。',
      title: 'Claude.ai',
      use_cases: [
        '基于数据表的记录，分析市场趋势和用户反馈',
        '智能汇总数据表记录并生成专业报告',
        '生成高质量的博客文章、新闻报道、社交媒体帖子',
      ],
    },
    deepseek: {
      apikey: 'DeepSeek API 密钥',
      apikey_help_text:
        '输入由 DeepSeek 提供的 API 密钥，您可以在 DeepSeek API Keys 找到它。https://platform.deepseek.com/api_keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: '自定义Base URL',
      custom_base_url_description:
        '一些第三方平台也提供 DeepSeek 模型的调用，因此你可以自行修改基础 URL（例如阿里云、火山方舟、硅基流动）。',
      custom_model: '自定义模型ID',
      custom_model_description: '您可以使用自定义模型 ID 来指定您想要使用的模型。',
      description:
        'DeepSeek-R1 是一种最先进的大型语言模型，经过强化学习和冷启动数据优化，具有卓越的推理、数学和代码性能。',
      organization_id: '组织 ID',
      organization_id_help_text: '组织 ID 是您组织的唯一标识符，可在 API 请求中使用。(通常不填)',
      title: 'DeepSeek',
    },
    delete_warning_content: '删除后无法还原，您确定要删除吗？',
    delete_warning_title: '删除配置',
    description_know_more: '查看帮助文档以了解更多信息',
    dingtalk: {
      description:
        '通过钉钉自定义机器人的 Webhook，实现自动化发送各类消息至钉钉群组。搭配自动化流程，可用于任务提醒、状态更新或项目汇报的自动通知。通过钉钉群组实现任务完成情况或重要信息的即时推送，增强企业内部的沟通和任务管理效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '钉钉自定义机器人',
    },
    explore_integration: '探索集成',
    features_list: 'Integrations List',
    feishu: {
      description:
        '通过飞书自定义机器人的 Webhook, 将消息发送至群组。与自动化流程配合使用，可在飞书平台上实现定期更新、告警通知或会议安排的自动推送。这种集成帮助团队成员在飞书群中及时了解重要动态，提升工作流程的透明度和信息共享效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '飞书自定义机器人',
    },
    general: {
      err_msg: '该配置项不能为空',
      note: '备注名称',
      note_placeholder: '请起一个易于记忆和辨识的名称',
    },
    github: {
      description:
        '将您的 Bika 帐号与 Github 帐号绑定，轻松实现通过 Github 帐号登录 Bika，安全便捷。',
      title: 'GitHub',
    },
    google: {
      description:
        '将您的 Bika 帐号与 Google 帐号绑定，轻松实现通过 Google 帐号登录 Bika，安全便捷。',
      title: 'Google',
    },
    googleai: {
      description:
        'Google AI 是由 Google 公司开发的一系列大语言模型（包含 Gemini 等），能够提供智能对话、内容生成和数据分析等服务。它能够理解复杂的查询，并提供精确的回答，帮助用户提高工作效率和创造力。',
      title: 'Google AI',
      use_cases: [
        '利用数据表记录进行深度市场分析,洞察用户需求和行为模式',
        '自动整合数据表信息,生成可视化的专业分析报告',
        '基于给定主题和关键词,智能创作引人入胜的文章和社交媒体内容',
      ],
    },
    imap: {
      create_new_integration: '连接新的 IMAP 邮箱',
      description:
        '通过配置 IMAP 邮箱账号，用户可以将接收邮件的功能集成到系统中。结合自动化流程，可以在收到特定邮件时触发相应的操作，如自动创建任务、归档邮件或触发警报。适用于需要从邮件中获取信息并做出响应的场景。',
      password_label: '密码',
      password_placeholder: '请输入密码',
      port_err_msg: '端口必须是数字',
      port_helper_text: '请输入 IMAP 服务器使用的端口号。常用端口是993。',
      port_label: '端口',
      port_placeholder: '请输入IMAP服务器使用的端口号',
      server_helper_text:
        '请输入接收邮件服务器地址 (IMAP)。如果您没有此信息，请联系您的电子邮件服务提供商。',
      server_label: 'IMAP 服务器',
      server_placeholder: 'imap.example.com',
      title: 'IMAP 邮箱账号',
      tls_label: '是否启用 TLS',
      user_name_label: '用户名',
      user_name_placeholder: '<EMAIL>',
    },
    integration: '集成应用',
    linkedin: {
      description:
        'LinkedIn 是一个专业的社交平台，旨在帮助用户建立职业网络、寻找工作机会并分享行业见解。通过集成 Bika 的自动化功能，您可以高效管理公司或个人信息。',
      title: '领英',
      use_cases: [
        '使用数据表记录在 LinkedIn 上发布新的博客文章',
        '使用数据表记录更新 LinkedIn 公司页面',
        '新表单提交时创建 LinkedIn 分享更新',
      ],
    },
    make: {
      description:
        'Make.com 是一个自动化平台，通过零代码或低代码解决方案帮助用户连接应用程序和服务，从而简化工作流程。通过集成 Bika 的数据表和自动化功能，使您的数据在平台之间无缝流动。',
      title: 'Make.com',
      use_cases: [
        '使用 Make.com 将数据发布到数千个应用程序',
        '将 Make.com 场景中的数据保存到数据表',
        '从 Make.com 场景创建新的数据表记录',
        '当有新的表单提交时，激活 Make.com 场景',
      ],
    },
    my_integration: '我的集成',
    mysql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'MySQL 用于连接和管理 MySQL 数据库，支持数据查询、插入、更新和删除，帮助用户高效地处理和存储数据。',
      host_helper_text: '请输入 MySQL 服务器地址。如果您没有此信息，请联系您的数据库管理员。',
      host_label: 'MySQL 服务器',
      host_placeholder: 'mysql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: '密码',
      password_placeholder: ' ',
      port_err_msg: '端口必须是数字',
      port_helper_text: '请输入 MySQL 服务器使用的端口号。',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'MySQL',
    },
    openai: {
      apikey: 'OpenAI API 密钥',
      apikey_help_text:
        '输入由 OpenAI 提供的 API 密钥，您可以在 OpenAI 账户设置中找到它。https://platform.openai.com/api-keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: '自定义Base URL',
      custom_base_url_description:
        '您可以自定义 OpenAI 的基础 URL。所有兼容 OpenAI 的 AI 模型 API 都将受到支持。 (如豆包、Kimi等)',
      custom_model: '自定义模型ID',
      custom_model_description: '您可以使用自定义模型 ID 来指定您想要使用的模型。',
      description:
        '使用 OpenAI 的 GPT 模型，您可以自动生成自然语言文本、进行智能对话、编写代码片段，或提供个性化建议等。',
      organization_id: '组织 ID',
      organization_id_help_text: '组织 ID 是您组织的唯一标识符，可在 API 请求中使用。(通常不填)',
      title: 'OpenAI',
    },
    page_description:
      '集成数百个其他应用、AI智能体和AI模型。通过使用Bika.ai，在第三方应用和您的技术栈之间创建复杂的自动化流程。',
    page_title: '第三方集成 | Bika.ai的AI工作流自动化',
    postgresql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'PostgreSQL 用于连接和管理 PostgreSQL 数据库，支持数据查询、插入、更新和删除，帮助用户高效地处理和存储数据。',
      host_helper_text: '请输入 PostgreSQL 服务器地址。如果您没有此信息，请联系您的数据库管理员。',
      host_label: 'PostgreSQL Server',
      host_placeholder: 'postgresql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: '端口必须是数字',
      port_helper_text: '请输入 PostgreSQL 服务器使用的端口号。',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'PostgreSQL',
    },
    siri: {
      description:
        '结合 Apple Siri、快捷指令和 Bika API，可以实现多种场景的自动化工作流。例如，用户可以通过 Siri 的语音指令，快速为自己或同事创建一条 Bika 待办任务，从而解放双手，提升工作效率。',
      title: 'Siri',
      use_cases: [
        '通过 Siri 语音指令和快捷指令查找数据表中的记录',
        '使用 Siri 和快捷指令在数据表中创建新记录',
        '使用 Siri 和快捷指令将照片上传到数据表',
        '通过 Siri 打开 Bika App 并查看特定数据表',
        '使用 Siri 和快捷指令同步数据表的事件至手机日历',
      ],
    },
    slack: {
      description:
        '利用 Slack应用的 Incoming Webhook，将消息发送到Slack频道。结合自动化流程，可以定时自动推送项目进展、任务完成或紧急公告的通知，确保团队成员在Slack中获取即时信息。',
      form_item_1_label: 'Incoming Webhook Url',
      form_item_1_placeholder: 'Incoming Webhook Url',
      title: 'Slack 应用',
    },
    smtp: {
      data_missing: '缺少数据',
      description:
        '配置基于 SMTP 协议的自定义发信邮箱。与自动化流程搭配，可以在特定事件触发时自动发送邮件，适用于任务完成通知、故障报警和定期报告发送、营销邮件群发等场景。',
      password_label: '密码',
      password_placeholder: '请输入密码',
      port_err_msg: '端口必须是数字',
      port_helper_text: '请输入SMTP服务器使用的端口号。常用端口是25、465和587。',
      port_input_err_msg: '请输入正确的端口',
      port_label: '端口',
      port_placeholder: '请输入SMTP服务器使用的端口号',
      server_helper_text:
        '请输入发件邮件服务器地址 (SMTP)。如果您没有此信息，请联系您的电子邮件服务提供商。',
      server_label: 'SMTP 服务器',
      server_placeholder: 'smtp.example.com',
      title: 'SMTP 邮箱账号',
      user_name_label: '用户名',
      user_name_placeholder: '<EMAIL>',
    },
    telegram: {
      description:
        '通过 Telegram Bot 的能力，发送消息至群组、频道或私聊会话。结合自动化流程，能够在事件触发时自动推送通知，如系统状态更新、事件提醒或团队动态，确保用户在Telegram平台上即时收到信息，便于事件管理和快速响应。',
      field_bot_token: '机器人令牌（Bot Token）',
      field_bot_token_placeholder: '请输入 Bot Token',
      option_manual_token: '手动输入',
      option_select_bot: '选择空间站内已有的集成',
      title: 'Telegram Bot',
      title_token: 'Bot Token',
    },
    tencenthunyuan: {
      description:
        '腾讯混元是由腾讯研发的大语言模型，具备强大的中文创作能力，复杂语境下的逻辑推理能力，以及可靠的任务执行能力。',
      title: '腾讯混元 (Tencent Hunyuan)',
      use_cases: [
        '提供文档创作、文本润色、文本校阅、表格及图表生成，提高创作效率',
        '提供会议答疑、会议总结、会议待办事项整理等功能，简化会议操作并提高效率',
        '智能化广告素材创作，提高营销内容制作效率',
        '构建智能导购，帮助商家提升服务质量和服务效率',
      ],
    },
    third_party_integration: '第三方集成',
    tongyiqianwen: {
      description:
        '通义千问是阿里云研发的大规模语言模型，能够生成各种类型的文本，如文章、故事、诗歌等，并能根据用户的需求提供定制化的回答和服务，帮助用户解决问题和完成任务。',
      title: '通义千问 (Qwen)',
      use_cases: [
        '对数据表中的记录进行汇总并生成报告',
        '基于数据表的记录，生成邮件正文',
        '生成博客文章、新闻稿等文本内容',
      ],
    },
    twitter: {
      create_new_integration: '连接到新的X(推特)账户',
      description:
        '通过 OAuth 方式连接 Twitter 账号，实现推文的自动化创建。结合自动化流程，能够在新闻发布、日常更新或营销活动等场景中自动推送推文，实现定时信息发布。有助于保持媒体帐号的活跃，增加与粉丝的互动。',
      form_item_1_label: 'Client ID',
      form_item_1_placeholder: '请输入 Client ID',
      form_item_2_label: 'Client Secret',
      form_item_2_placeholder: '请输入 Client secret',
      title: 'X(Twitter) OAuth2.0',
    },
    twitter_oauth_1a: {
      access_token_label: 'Access Token',
      access_token_placeholder: '输入 Access Token',
      access_token_secret_label: 'Access Token Secret',
      access_token_secret_placeholder: '输入 Access Token Secret',
      api_key_helptext:
        '你可以在 Twitter 开发者平台中找到 Consumer Key， https://developer.x.com/en/portal/projects-and-apps',
      api_key_label: 'API Key',
      api_key_placeholder: '输入 API Key（Consumer Key）',
      api_secret_label: 'API Secret',
      api_secret_placeholder: '输入 API Secret（Consumer Secret）',
      description:
        '通过 OAuth1.0a User Context 方式连接 Twitter 账号，实现图片、动图、视频资源的上传、编辑。结合自动化流程，可以发布带有媒体素材的推文。',
      title: 'X(Twitter) OAuth1.0a',
    },
    vika: {
      description:
        'Vika 用于连接和管理 Vika 数据库，支持数据查询、插入、更新和删除，帮助用户高效地处理和存储数据。',
      title: 'Vika',
      vika_token: 'Vika API Token',
      vika_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    },
    webhook: {
      description:
        '使用 Webhook 接收和处理来自外部系统的 HTTP 请求。结合自动化功能，它可以在接收到特定事件时自动触发数据更新、通知或工作流执行等操作。这种集成有助于简化流程，确保对外部触发器的及时响应，从而提高整体系统的效率和连接性。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: 'Webhook',
    },
    wechat: {
      description: '将Bika帐号与您的微信帐号进行绑定，实现微信扫码即可登录 Bika，方便快捷。',
      title: '微信登录',
    },
    wecom: {
      description:
        '通过企微群机器人的 Webhook，实现自动化发送消息至企微群组。结合自动化流程，可用于企业内部实时推送项目更新、系统通知或会议提醒。它能确保团队成员在微信群中获取到即时且重要的通知，有助于提升团队协作和信息传递的效率。',
      form_item_1_label: 'Webhook Url',
      form_item_1_placeholder: 'Webhook Url',
      title: '企业微信群机器人',
    },
    zapier: {
      description:
        'Zapier 是一个自动化平台，通过零代码或低代码解决方案帮助用户连接应用程序和服务，从而简化工作流程。通过集成 Bika 的数据表和自动化功能，使您的数据在平台之间无缝流动。',
      title: 'Zapier',
      use_cases: [
        '使用 Zapier 将数据发布到数千个应用程序',
        '从 Zapier 自动化中创建新的数据表记录',
        '当有新的表单提交时，激活 Zapier 自动化',
        '从 Zapier 自动化中更新数据表记录',
        '从 Zapier 自动化中删除数据表记录',
      ],
    },
    zoom: {
      description:
        'Zoom 集成允许用户在系统中直接安排和管理 Zoom 会议。通过自动化流程，可以在特定事件触发时自动创建会议、发送邀请或提醒参会人员。适用于需要方便快捷地管理线上会议和视频通话的场景',
      title: 'Zoom',
      use_cases: [
        '为新的表单提交创建 Zoom 网络研讨会注册人',
        '在事件触发时自动创建 Zoom 会议',
        '向参与者发送 Zoom 会议提醒',
        '将新的 Zoom 录音上传到数据表',
        '生成 Zoom 会议报告和摘要',
      ],
    },
  },
  invite: {
    copy_link: '复制链接',
    create_invite_description:
      '通过您创建的链接加入团队的成员将被自动分配到相应的小组和角色，并且双方都会获得',
    create_invite_loading: '创建中',
    create_public_invitation_link: '创建公开邀请链接',
    created_public_invitation_link: '已创建的公开邀请链接',
    delete_link: '删除链接',
    email_invite_table: {
      createdAt: '邀请时间',
      delete: '删除',
      email: '邮箱',
      operation: '操作',
      resend: '重新发送',
      status: '状态',
    },
    input_invite_email_invalid: '邮箱格式不正确',
    input_invite_member_email: '输入邮箱',
    input_invite_one_or_more_email: '输入一个或多个电子邮件地址，用回车键确认',
    invite: '邀请',
    invite_by_email_desc: '通过邮箱邀请加入的成员，双方都获得',
    invite_by_email_desc_next: '可用于升级空间站、购买高级模板、兑换纪念品。',
    invite_description:
      '经由该链接加入的团队成员会被自动划分至该小组与角色之中，在成功加入之后，链接的创建者以及首次加入空间站的成员皆可获取',
    invite_description_next: '此币能够用于空间站的升级、高级模板的购置以及纪念品的兑换。',
    invite_identify: '邀请身份',
    invite_identify_guest: '访客',
    invite_identify_guest_desc:
      '合作伙伴或外部协作者，被邀请进入空间站参与特定任务，默认无法查看节点资源',
    invite_identify_member: '成员',
    invite_identify_member_desc: '组织或团队的内部协作者，共同参与项目工作',
    invite_link_created_by: '创建的邀请链接',
    invite_link_created_fail: '创建失败',
    invite_link_created_success: '创建成功',
    invite_link_have_coins: '你当前有',
    invite_members: '邀请成员',
    invite_outsider_invite_input_already_exist: '邮箱输入重复',
    invite_people: '邀请人员',
    invite_people_by_email: '通过邮箱邀请',
    invite_people_by_email_button: '邀请',
    invite_people_by_email_description: '用逗号分隔多个电子邮件地址',
    invite_people_by_email_placeholder: '电子邮件地址',
    invite_people_by_link: '通过链接邀请',
    invite_people_by_link_button: '复制',
    invite_people_by_link_copied: '已复制链接',
    invite_people_by_link_description: '分享此链接邀请人员加入您的空间站',
    invite_people_description: '邀请人员加入您的空间站',
    invite_record: '邀请记录',
    invite_record_description:
      '当被邀请者加入后，邀请者和被邀请邀请者都可获得奖励，可用于升级空间站、购买高级模板、兑换纪念品。',
    invite_role: '角色（可选）',
    invite_role_placeholder: '请选择角色',
    invite_status: {
      accepted: '已接受',
      pending: '待接受',
      rejected: '已拒绝',
    },
    invite_team: '小组（必选）',
    invite_team_placeholder: '请选择小组',
  },
  launcher: {
    ai: 'AI',
    ai_launcher: 'AI 启动器',
    ai_launcher_description:
      'AI 启动器是一款智能快捷功能，旨在帮助用户高效地执行各种操作。通过简洁直观的界面，您可以轻松地执行命令、查找文件、配置空间站等。无论是日常任务还是复杂操作，AI 启动器都能提供快速高效的解决方案，大幅提升工作效率。',
    ask_me_anything: '问我任何问题...',
    chat_history: '聊天记录',
    chat_replay: '聊天回放',
    command: '命令',
    command_not_found: '未找到结果,请询问 AI。',
    commands: '指令',
    commands_placeholder: '你想执行哪些指令...',
    database_record: '数据记录',
    database_record_placeholder: '找到你要的数据记录...',
    document: '文档',
    document_placeholder: '找到你要的文档..',
    file: '文件',
    file_placeholder: '按名称查找文件或文件夹...',
    getting_started: '新手入门',
    help: '帮助',
    help_placeholder: '你想了解什么...',
    launcher: '启动器',
    mission: '智能任务',
    node: '节点资源',
    pending_todos: '待办任务',
    recently: '最近',
    record: '记录',
    reminder: '提醒',
    router: '路由器',
    search_tip: '支持拼音模糊搜索',
    shortcuts: '收藏',
    smart: '智能',
    task: '待办',
    templates: '模板',
    ui_modal: 'UI 模态',
    unread_reports: '未读报告',
    url: 'URL',
  },
  license: {
    apply_description: '获取 Bika.ai 私有化安装包及教程。',
    apply_title: 'Bika.ai 私有化版安装申请',
    company_name: '贵公司/团队的名称是什么？',
    company_size: '贵公司/团队有多少人？',
    copy_your_license_key: '在这里复制您的许可证密钥',
    create_self_host_license_key: '创建私有化部署的许可证密钥(beta)',
    download_and_install_self_host: '下载并安装私有化',
    get_self_host_license_key: '获取私有化部署的许可证密钥（License Key）',
    get_trial: '你可以获得一次 30 天的试用机会',
    industry: '贵公司所在行业是什么？',
    license_key: '许可证密钥',
    license_key_expired: '许可证密钥已过期',
    license_key_expired_desc: '您的试用期已过期',
    please: '请',
    submit: '安装 Bika.ai 私有化包',
    trial_desc: '目前，您正处于30天的试用期内。如果您在使用过程中遇到任何问题',
  },
  mission: {
    after_action: '任务完成后的下一步动作',
    allow_complete_manually: '允许手动完成',
    allow_reject: '允许拒绝',
    assign_type: '分配类型',
    assign_type_dedicated: '专属任务',
    assign_type_dedicated_description: '任务会分配给多个成员，但每个成员必须独立完成任务',
    assign_type_shared: '共享任务',
    assign_type_shared_description: '任务会分配给多个成员，任何一个成员完成任务后即标记为已完成',
    assigned: '分配给',
    assignee: '分配人',
    button_text: '按钮文字',
    can_transfer: '允许转移',
    complete: '标记完成',
    completedAt: '完成时间',
    confirm: '同意',
    createAt: '发起时间',
    create_record: '创建记录',
    create_success: '创建任务成功',
    database_record: '数据表记录',
    database_view: '数据表视图',
    date_time_now: '现在',
    date_time_today: '今天',
    description: '任务内容',
    description_placeholder: '请输入任务内容',
    detail: '查看详情',
    dueAt: '截止时间',
    dynamic_datetime: '动态日期时间',
    dynamic_datetime_type: '动态日期时间类型',
    end_time: '结束时间',
    features_list: 'Missions List',
    force_popup: '强制弹出',
    go_to_mission: '前往任务',
    initiator: '发起人',
    know_and_next: '已了解，进入下一步 ',
    mission: '智能任务',
    mission_description:
      'Mission is a smart, automative, traceable tasks differs from typical tasks or to-do lists, which you have to check off by yourself.\nFor example, consider the Create Record Mission: when a user receives it, the mission will automatically be marked as complete only when the required record has been created.',
    mission_name: '任务名称',
    mission_name_placeholder: '请输入任务名称',
    mission_type: '智能任务类型',
    modal_content_mission_invalid: '该任务所属的资源已经无法访问，是否删除任务',
    modal_title_mission_invalid: '温馨提示',
    more_setting: '更多设置',
    msg_mission_completed: '任务被完成！',
    msg_mission_rejected: '任务已拒绝。',
    msg_no_next_action: '没有下一步操作。',
    msg_transfer_not_supported: '目前不支持转交任务。',
    next: '下一步',
    placeholder_assign_type: '请选择分配类型',
    processing: '处理中',
    progress: '进度',
    reject: '拒绝任务',
    reminder: '任务的提醒时间',
    reminder_description: '提醒描述',
    reminder_title: '提醒标题',
    reminder_to: '提醒对象',
    repeat: '设置重复提醒',
    seconds: '({seconds}秒)',
    show_end_time: '设置结束时间',
    show_start_end_time: '设置任务的开始和结束时间',
    show_start_time: '设置开始时间',
    specific_datetime: '特定日期时间',
    start_time: '开始时间',
    taskIntroduction: '任务介绍',
    taskObjective: '任务目标',
    taskStatus: '任务状态',
    time_internal: '时间间隔',
    tips_for_readme_mission: '在新标签页打开此文档，有助于您边看边实践。',
    transfer: '转交任务',
    type: {
      ai_create_records: {
        description: 'AI 会根据你的自然语言输入，识别对应数据结构，自动创建记录',
        name: 'AI 创建记录',
      },
      approval: {
        description: '只有一个步骤的任务，任务负责人可以点击同意、拒绝、或移交他人。比如请假审批',
        name: '审批任务',
      },
      comment_record: {
        description: '收到该任务的成员，需要对指定的记录进行评论，方可完成任务',
        name: '对记录进行评论',
      },
      create_multi_records: {
        description: '收到该任务的成员，需要创建指定数量的数据表记录',
        name: '创建若干条记录',
      },
      create_record: {
        description: '收到该任务的成员，需要创建指定数据表的一条记录',
        name: '创建记录',
      },
      create_task: {
        description: '收到该任务的成员，需要创建一个新的任务',
        name: '创建任务',
      },
      enter_view: {
        description: '收到该任务的成员，会被引导查看数据表的指定视图',
        name: '查看指定视图',
      },
      google_meet: {
        description: '带有 Google 会议链接的任务，适合用于线上会议的邀约',
        name: 'Google 会议',
      },
      install_template: {
        description: '要求收到该任务的成员完成一次模板的安装',
        name: '安装模板',
      },
      invite_member: {
        description: '要求收到该任务的成员完成一次邀请成员的操作',
        name: '邀请成员',
      },
      quest: {
        description: '由多个业务相关性的子任务共同构成的一个任务集。比如新手任务场景',
        name: '系列任务',
      },
      read_markdown: {
        description: '收到该任务的成员，要求查看指定的 Markdown 文档',
        name: '查看 Markdown 文档',
      },
      read_template_readme: {
        description: '收到该任务的成员，要求查看指定模板的说明文档',
        name: '阅读模板说明',
      },
      redirect_space_node: {
        description: '收到该任务的成员，会被引导至空间站的指定节点',
        name: '跳转到空间站节点',
      },
      reminder: {
        description: '收到该任务的成员，会收到一条提醒消息',
        name: '提醒',
      },
      review_record: {
        description: '收到该任务的成员，会被引导查看数据表的某条记录',
        name: '查看指定记录',
      },
      sequence: {
        description: '包含多个步骤，需按顺序完成的任务。例如多层级的审批工单',
        name: '序列任务',
      },
      set_space_name: {
        description: '收到该任务的成员，会被引导至空间站设置界面，完成空间站名称的设置',
        name: '设置空间站名称',
      },
      submit_form: {
        description: '创建任务并指派给某成员，请求他填写指定的表单',
        name: '提交表单',
      },
      submit_multiple_form: {
        description:
          '收到任务的成员，需要将指定的若干份表单全部填写完毕，该任务才会标记完成。例如合同订单的录入，销售需要填写客户、合同、付款记录三份表单',
        name: '提交若干份表单',
      },
      ui_launcher: {
        description: '用于启动用户界面相关的功能或应用',
        name: 'UI 启动器',
      },
      update_record: {
        description: '收到该任务的成员，需要完成对指定的记录的编辑，方可完成任务',
        name: '更新记录',
      },
      voov_meet: {
        description: '带有 Voov 会议链接的任务，适合用于线上会议的邀约',
        name: 'Voov 会议',
      },
      zoom_meet: {
        description: '带有 ZOOM 会议链接的任务，适合用于线上会议的邀约',
        name: 'Zoom 会议',
      },
    },
    update_record: '更新记录',
  },
  navbar: {
    agent_builder: '生成助手',
    agent_builder_description: '我是你的生成助手，我不招聘人，我只建立智能体',
    beta: '测试',
    chief_of_staff: '超级助理',
    chief_of_staff_description: '我是你的超级助理，可协助你完成信息检索、内容生成等各类工作',
    expert: '专家',
    explore: '探索',
    home: 'AI总助',
    personal: '个人',
    personal_resources: '个人资源',
    private: '私人资源',
    private_description: '私人资源：专属你的资源空间，可自由编辑、新增或删除资源',
    report: '智能报告',
    resources: '资源',
    shortcuts: '捷径',
    shortcuts_description: '捷径：包含管理员或自行设置的捷径，快速访问常用资源',
    shortcuts_resources: ' 快捷方式资源',
    smart: '智能',
    smart_description: '智能：支持全局搜索、智能任务与智能报告，同时配备回收站和模板应用',
    space_launcher: '空间启动器',
    space_launcher_description: '空间的启动器',
    super_agent: '超级助理',
    team: '团队',
    team_resources: '资源',
    team_resources_description:
      '团队资源：与团队成员共同编辑、新增或删除资源，便于团队协作与资源共享',
    todo: '智能任务',
  },
  node: {
    delete_node: '删除节点',
    delete_node_description: '确定删除节点「{name}」吗？',
    delete_node_success: '删除节点"{name}"成功',
    edit_folder: '编辑文件夹',
    empty_folder: '空文件夹',
    export_attachments: '导出附件',
    export_bika_file: '导出 .bika 文件',
    export_excel: '导出为 Excel 文件',
    export_template: '导出 .bika 模板(内部)',
    import_bika_file: '从 .bika 文件导入',
    jump_to_node: '跳转到节点',
    node: '节点',
    node_detach: '脱离模板',
    node_detach_fail: '脱离模板失败',
    node_detach_success: '脱离模板成功',
    node_guide: {
      automation: {
        description: '自动化功能让您可以设置触发器和动作，实现工作流程的自动化处理。',
        feature1: '• 多种触发条件',
        feature2: '• 丰富的动作类型',
        feature3: '• 条件分支逻辑',
        feature4: '• 执行历史记录',
        tip1: '💡 可以设置多个触发条件',
        tip2: '💡 建议先测试自动化规则再启用',
        title: '欢迎使用自动化',
      },
      dashboard: {
        description: '仪表板帮您将数据可视化，创建图表和报表来洞察数据趋势。',
        feature1: '• 多种图表类型',
        feature2: '• 实时数据更新',
        feature3: '• 自定义布局',
        feature4: '• 数据筛选器',
        tip1: '💡 可以添加多个数据源',
        tip2: '💡 支持导出图表和数据',
        title: '欢迎使用仪表板',
      },
      database: {
        description: '数据表是 Bika 的核心功能，让您可以结构化地存储和管理数据。',
        feature1: '• 创建自定义字段类型',
        feature2: '• 多种视图展示数据',
        feature3: '• 强大的筛选和排序功能',
        feature4: '• 支持协作和权限管理',
        tip1: '💡 可以通过拖拽调整字段顺序',
        tip2: '💡 使用视图功能可以创建不同的数据展示方式',
        title: '欢迎使用数据表',
      },
      default: {
        description: '这是一个强大的功能模块，让您的工作更加高效。',
        feature1: '• 直观易用的界面',
        feature2: '• 强大的功能特性',
        feature3: '• 灵活的配置选项',
        tip1: '💡 探索各种功能来发现更多可能性',
        title: '欢迎使用此功能',
      },
      form: {
        description: '表单让您可以轻松收集和整理信息，数据会自动同步到关联的数据表中。',
        feature1: '• 拖拽式表单设计器',
        feature2: '• 多种字段类型支持',
        feature3: '• 自动数据验证',
        feature4: '• 支持条件逻辑',
        tip1: '💡 可以设置字段的显示条件',
        tip2: '💡 表单提交的数据会实时同步到数据表',
        title: '欢迎使用表单',
      },
      got_it: '我知道了',
      main_features: '主要功能：',
      tips: '使用技巧：',
    },
    node_info: '文件信息',
    node_name: '节点名称',
    node_update: '更新模板',
    permission: {
      add_permission_message: '请添加需要设置权限的成员、小组或者角色。',
      can_comment: '可以评论',
      can_edit: '可以编辑',
      can_edit_content: '仅可更新',
      can_edit_content_desc: '可以对已有内容进行更新，数据表中不能增删除记录。',
      can_edit_desc: '可以对内容进行编辑，数据表中可以增删记录。',
      can_view: '可以查看',
      can_view_desc: '不能编辑，只可查看节点资源的内容。',
      description:
        '您可以根据不同角色、成员、部门，进行精细地分配权限，从而有效控制数据访问和操作权限。通过权限管理，您可以确保每个团队成员只能访问和操作与其职责相关的功能和数据，大大提高了数据的安全性和隐私性。',
      full_access: '可以管理',
      full_access_desc: '拥有节点资源的所有操作权限。',
      login_to_edit: '登录后可以编辑',
      no_access: '禁止访问',
      no_access_desc: '无权限查看该资源。',
      permission_description: {
        CAN_COMMENT: '您可以查看并评论该资源',
        CAN_EDIT: '您可以编辑内容并管理数据库中的记录',
        CAN_EDIT_CONTENT: '您可以更新现有内容，但不能增删记录',
        CAN_VIEW: '您只能查看该资源的内容',
        FULL_ACCESS: '您拥有该资源的所有操作权限',
        NO_ACCESS: '您没有权限访问该资源',
      },
      remove: '移除权限',
      title: '权限管理',
    },
    publish_to_template_center: '发布到模板中心',
    republish: '再次发布',
    tutorial: 'Tutorial',
    view_original_template: '查看原模板',
  },
  notification: {
    all: '全部',
    all_notifications_marked_as_read: '所有通知已标记为已读',
    app_notification: 'App 通知',
    app_push_notification: 'App 推送通知',
    browser_notification: '浏览器通知',
    clean_all_notifications: '清除所有通知',
    confirm_to_clean_all_notifications: '确认清除所有通知?',
    mail_notification: '邮件通知',
    mark_all_as_read: '全部标记为已读',
    new_agenda: '新议程',
    new_mission: '新任务',
    new_report: '新报告',
    no_notification_so_far: '暂无通知',
    notification: '通知',
    notification_settings: '通知设置',
    notification_type: '通知类型',
    sms_notification: '短信通知',
    system_message: '系统消息',
    unread: '未读',
  },
  ok: '确定',
  pages: {
    get_started: '开始使用',
  },
  pagination: {
    load_more: '加载更多',
    loading: '加载中...',
    no_more: '没有更多了',
  },
  pricing: {
    business: '商业版',
    change_your_plan: '更改您的计划',
    community: '社区版',
    currency_symbol: '¥',
    customize: '定制',
    customize_seat: '定制席位',
    enterprise: '企业版',
    enterprise_private_cloud: '专有云版',
    enterprise_self_hosted: '私有化版',
    experience_now: '立即体验',
    features: {
      advanced_automation_integrations: '高级自动化集成',
      advanced_automation_integrations_tips:
        '高级自动化触发器和连接器，用于连接外部工具，如高级AI模型等',
      api_calls_per_month: '每月API调用次数',
      api_rate_limits: 'API速率限制',
      authorized_email_domain: '授权的电子邮件域名',
      automation_integrations: '自动化集成',
      automation_integrations_tips: '自动化触发器和执行器，用于连接外部工具',
      automation_run_history: '自动化运行历史保留天数',
      automation_runs_per_month: '每月的自动化运行次数',
      browser_notifications: '浏览器通知',
      byok_support: '使用私有的AI API KEY',
      coming_soon: '即将推出',
      community: '社区',
      credits_per_seat_per_month: '每位成员每月获得AI积分',
      credits_per_seat_per_month_tips:
        '积分将发放至空间站，按“成员数 × 每月积分”计算，并于每月重置。积分用于调用 AI 模型。',
      custom_domain: '自定义域名',
      data_sync: '数据同步',
      email_support: '邮件支持',
      export_bika_file: '导出 .bika 文件',
      export_bika_file_tips:
        '.bika 是一种文件格式，可以将完整的数据和结构（如关联、自动化等）导入到 Bika 中',
      export_excel_csv: '导出 Excel/CSV 文件',
      help_center: '帮助中心',
      im_support: '企微专属客服',
      im_support_tips: '专属客服将在企业微信中为您提供专业的技术支持',
      integration_instances: '集成实例',
      managed_emails_per_month: 'Bika邮件服务(每月)',
      managed_emails_per_month_tips: 'Bika官方邮件服务,在自动化流程中将使用bika域名发送邮件',
      max_guest: '访客上限',
      max_records_per_database: '单个数据表的记录总数',
      max_records_per_space: '空间站的记录总数',
      max_seat: '人数上限',
      mirror_sync: '镜像',
      missions_per_month: '每月的任务总量',
      mobile_notifications: 'APP 通知',
      planned_feature: '规划中的功能',
      private_template: '私有模板',
      professional_services: '专属 V+ 顾问',
      publish_and_share: '发布与分享',
      publish_template: '发布模板',
      remove_logos: '移除品牌标识',
      remove_logos_tips: '界面中将不会出现 Bika 的品牌标识',
      reports_per_month: '每月的报告总量',
      resource_nodes: '资源节点总数',
      resource_permissions: '资源权限',
      self_hosted: '私有化部署',
      self_hosted_tips:
        '在自己的服务器上部署 Bika，甚至可以将安装实例进行白标（即去除品牌标识，进行品牌定制）',
      sell_template: '销售模板',
      sms_notifications: '短信通知',
      smtp_emails_per_month: 'SMTP邮件数',
      space_audit_log: '空间站审计日志',
      space_sessions_log: '空间站会话日志',
      storage: '存储容量',
      storage_tips: '单个工作空间中所有数据库和文档中存储的附件总存储上限',
      sub_admin: '子管理员',
      sub_domain: '子域名',
      unlimited: '无限制',
      user_sessions_log: '用户会话日志',
      webinar: '在线会议',
    },
    for_businesses_and_enterprises: '面向私有化部署和大型企业',
    for_individual_and_teams: '面向个人和团队',
    free: '免费',
    free_number: '¥0',
    free_trial_7_days: '7 天免费，之后每年 {price}',
    includes_word: '包括',
    modal_title: '更改您的计划',
    month: '月',
    monthly: '每月',
    oncely: {
      oncely_code: 'Oncely 兑换码',
      oncely_code_management: '兑换码管理',
      oncely_code_placeholder: '请输入 Oncely 兑换码',
    },
    page_section_detail_title: '比较计划和功能',
    page_section_question_title: '产品价格问答',
    payment_successful: '恭喜你升级 {plan} 成功！',
    payment_successful_description: '你已经获得下方特权：',
    per_seat: '/ 人',
    plus: 'Plus版',
    popular: '最受欢迎',
    price: '价格',
    pro: 'Pro版',
    question: {
      answer_1: '答案 1',
      question_1: '问题 1',
    },
    renew_and_cancel: '计划每年自动续订，直至取消',
    seat: '席位数',
    subscription_cycle: '每位成员/每月',
    team: '团队版',
    user: '用户',
    view_benefit_details: '查看权益详情',
    view_detail: '查看详情',
    year: '年',
    yearly: '每年',
  },
  publish_template: {
    allow: '允许',
    allow_detach_description:
      '允许后，安装过模板的用户，可以脱离模板，脱离后，无法从原模板升级，但可以用于二次发布',
    allow_users_to_detach_template: '允许其他用户脱离模板',
    author_space_title: '显示空间站名称',
    author_user_title: '显示我的昵称',
    cancel: '取消',
    category: '分类',
    coming_soon: '敬请期待的模板应用',
    coming_soon_description: '所有用户可以在模板应用搜索到你的模板，但是不能安装',
    configure_template: '配置模板',
    forbid: '禁止',
    init_mission: '初始化任务',
    init_mission_description: '你可以为该模板设置初始化任务，更好的引导使用者',
    keywords: '关键词',
    private: '私有',
    private_description: '私有则仅空间站成员可见，公开则所有 bika 用户都可见',
    public: '公开的模板应用',
    public_description: '所有用户可以在模板应用「公开的模板应用」中搜索和安装你的模板',
    publish_data: '发布数据',
    publish_data_description: '是否将表内数据一起发布数据到模板中心',
    publish_success: '发布成功',
    publish_template: '发布模板',
    publish_to_template_center: '发布到模板中心',
    space: '空间站内的模板应用',
    space_description: '仅限该空间站成员可以在模板应用「空间站内的模板应用」下搜索和安装你的模板',
    template_author: '作者信息',
    template_id: '模板 ID',
    template_published: '模板已发布',
    template_published_description_coming_soon:
      '所有用户可以在模板应用搜索到你的模板，但是不能安装',
    template_published_description_public:
      '所有用户可以在模板应用「公开的模板应用」中搜索和安装你的模板',
    template_published_description_space:
      '仅限该空间站成员可以在模板应用「空间站内的模板应用」下搜索和安装你的模板',
    template_visibility: '模板公开',
    use_cases: '使用场景',
    version_already_exist: '版本已存在',
    version_description: '版本说明',
    version_number: '版本号',
    view_template_center: '去模板中心查看',
  },
  record: {
    active: '动态',
    activity: {
      anonymous: '匿名者',
      comment_tip: 'Shift+Enter 换行，Enter 发送',
      empty_activity: '暂无动态',
      empty_comment: '暂无评论',
      just_changelog: '仅修改历史',
      just_comment: '仅评论',
      load_more: '加载更多',
      loading: '加载中...',
      no_more: '没有更多了',
      record_comment_and_change: '记录的评论和修改历史',
    },
    add_attachment: '添加附件',
    add_local_file: '添加本地文件',
    create: '创建',
    create_record: '创建记录',
    create_record_button: '创建记录',
    create_record_description: '创建新记录',
    create_record_failed: '创建记录失败',
    create_record_success: '创建记录成功',
    delete_comment: '删除评论',
    delete_comment_description: '确定要删除这条评论吗？',
    delete_record: '删除记录',
    drop_file_upload: '拖放文件上传',
    empty_comment: '暂无评论',
    first_record: '第一条记录',
    go_next: '下一条记录',
    go_previous: '返回上一条记录',
    input_comment_placeholder: '请输入评论，@提及某人',
    max_file_size: ': 文件最大不超过 500MB',
    modify_record: '修改记录',
    no_next_record: '没有下一条记录',
    no_previous_record: '没有上一条记录',
    paste_or_drop_file_upload: '粘贴或拖放至这里上传',
    record: '记录',
    record_comment: '记录评论',
    record_delete: '记录删除',
    record_detail: '记录详情',
    record_detail_description:
      '在 Bika.ai 中用户可以点开每一条记录，记录详情是该条记录的展开视图，包含该条记录的所有详细信息。',
    record_pin: '记录置顶',
    record_unnamed: '未命名记录',
    request_modify: '请求修改',
    request_new_record: '请求新建记录',
    select_date: '选择日期',
    select_from_files: '从文件选择',
    select_from_gallery: '从相册选择',
    select_member: '选择成员',
    select_option: '选择选项',
    tab_general: '基础',
    take_photo_or_record_video: '拍照或录制视频',
  },
  redeem: {
    oncely: {
      congratulations: '恭喜您！',
      contact_service: '联系客服',
      email: '电子邮箱',
      email_code: '验证码',
      enter_space: '进入空间站',
      input_email: '请输入邮箱地址',
      input_email_code: '请输入验证码',
      input_oncely_code: '请输入兑换码',
      logout: '退出登录',
      new_user_tip:
        '请知悉，通过兑换码将获得一个包含付费权益的全新空间站，旧空间站不支持兑换码升级！',
      new_user_tip_ignore_code:
        '激活成功后，将创建一个新的空间站，可全面使用对应套餐的所有功能。请注意，现存空间站无法通过此方法升级。',
      old_user_tip:
        '检测到你曾经注册过，已有 {spaceCount} 个空间站存在。激活成功后，将创建一个新的空间站，可全面使用对应套餐的所有功能。请注意，现存空间站无法通过此方法升级。',
      oncely_code: '兑换码',
      question: '遇到问题',
      reedem_oncely: '兑换高级协作空间',
      submit: '提交',
      success_redeem: '✨ 新空间已创建，包含的高级功能已解锁！',
      you_have_used_fragment_1: '您当前已登录 ',
      you_have_used_fragment_2:
        '。点击下面的“提交”按钮，立即创建一个新空间，并激活您的订阅方案对应的所有功能。',
    },
  },
  referral: {
    bika_coins_description: '积分可用来兑换各种服务和产品',
    check_usage: '检查使用情况',
    current_space_plan: '当前空间站计划',
    earn_bika_coins: '赚取积分',
    method_1: '方法1：通过邀请链接邀请',
    method_2: '方法2：通过邀请码邀请',
    method_3: '方法3：邀请加入空间',
    method_4: '方法4：安装移动应用程序',
    other_referral_code: '邀请码',
    referral: '推荐',
    referral_code: '邀请码',
    referral_rewards: '填写他人的邀请码，双方都可获得',
    reward_history: '奖励历史',
    total: '总计：',
    view_my_referral_code: '查看我的邀请码',
    your_bika_coins: '你的积分',
  },
  reminder: {
    no_reminder_so_far: '暂无提醒',
    remind: '提醒',
    remind_me: '提醒我',
    reminder: '提醒',
    reminders: '提醒',
  },
  report: {
    create_report: '发送报告',
    create_report_description:
      '您可以通过自动化流程触发生成报告，报告内容会以Markdown等格式发送至指定人员或群组。此报告为AI根据您的设定自动生成数据和信息，以帮助您更好地了解项目进展。',
    mark_all_as_read: '全部标记为已读',
    mark_all_as_read_content: '确定是否一键将所有未读报告标记为已读',
    mark_as_read: '标记为已读',
    no_report_so_far: '没有新的报告需要查看',
    read: '已读报告',
    read_report: '已阅读报告',
    report: '报告',
    report_description:
      '由AI或自动化基于设定的规则或数据进行生成报告材料，形式类似于一篇邮件、文章或者文档。',
    report_detail: '报告详情',
    report_detail_description:
      '展示报告的详细信息，每当自动化流程结束后AI会根据用户设定的内容自动生成报告，帮助用户更好地回顾工作的进程。',
    report_info: '报告信息',
    reports: '报告',
    unread: '未读报告',
  },
  resource: {
    add_filter_condition: '添加筛选条件',
    add_shortcut_success: '添加到捷径成功',
    ai_page: {
      settings_html_copilot: 'AI 助手',
      settings_html_description: '在下方输入 HTML 页面代码，点击保存后，页面将会自动生成',
      settings_html_placeholder: '请输入 HTML 代码',
      settings_html_title: 'HTML 页面',
      welcome: '在右侧告诉 AI 助手你的想法，它会为你生成一个页面',
    },
    all_resources: '所有资源',
    automation_name: '自动化名称',
    can_not_create_integration: '无权限添加集成，找管理员配置',
    cancel_excel_import: '取消导入',
    cancel_excel_import_description: '你确定要取消导入么',
    cancel_incremental_import: '取消增量导入',
    cancel_incremental_import_description: '你确定要取消增量导入么',
    change_cover: '修改封面',
    change_form_logo: '修改Logo',
    close_export_modal_warning: '你正在导出数据中，确定要关闭吗？',
    content_changed_warning: '数据表内容已被更新，请刷新查看最新内容',
    content_is_empty: '内容为空',
    create_ai_agent_success: '创建 AI 智能体 "{name}" 成功',
    create_ai_page_success: '创建 AI 页面 "{name}" 成功',
    create_automation_action_success: '创建执行器成功',
    create_automation_success: '创建自动化流程 "{name}" 成功',
    create_dashboard_success: '创建仪表盘 "{name}" 成功',
    create_database_success: '创建数据表 "{name}" 成功',
    create_document_success: '创建文档 "{name}" 成功',
    create_folder_success: '创建文件夹 "{name}" 成功',
    create_form_success: '创建表单 "{name}" 成功',
    create_from_blank: '从空白创建',
    create_from_blank_automation_description: '从头构建自动化流程',
    create_from_blank_dashboard_description: '创建新仪表板以可视化数据',
    create_from_blank_database_description: '创建新数据表以存储数据',
    create_from_blank_document_description: '创建新文档以编写内容',
    create_from_blank_folder_description: '创建新文件夹以组织资源',
    create_from_template:
      '从下方中选择一个符合场景的的模板快速开始，如果没有合适的模板，也可以点击“创建空白资源”进行自定义搭建。',
    create_integration: '新建集成',
    create_mirror_success: '创建镜像 "{name}" 成功',
    create_view: '添加视图',
    create_view_success: '创建视图 "{name}" 成功',
    dashboard_description: '仪表盘描述',
    dashboard_name: '仪表盘名称',
    data_is_fetching: '数据获取中，请稍等...',
    day: '天',
    delete_field: '删除字段',
    delete_field_description: '删除字段后将无法恢复，确定要删除字段 ',
    delete_folder_error: '删除文件夹 "{name}" 失败',
    delete_folder_success: '删除文件夹 "{name}" 成功',
    delete_resource_description: '确定要删除资源 "{name}" 吗?',
    delete_resource_error: '删除资源 "{name}" 失败',
    delete_resource_success: '删除资源 "{name}" 成功',
    delete_view: '删除视图',
    delete_view_description: '您确定要删除视图：{name}？',
    delete_view_error: '删除视图 "{name}" 失败',
    delete_view_success: '删除视图 "{name}" 成功',
    description: '节点资源是一种特殊的资源，它可以是数据库、自动化、表单等节点实现。',
    download_again_file: '下载失败？点此重新下载',
    download_done_file: '已解析并开始下载，您可以在浏览器的下载记录中查看',
    download_loading_file: '正在处理您的请求，可能需要几分钟。请勿刷新或返回，以免取消操作',
    download_template: '下载模板.xlsx',
    edit_automation: '编辑自动化流程',
    edit_automation_action: '编辑执行器',
    edit_automation_trigger: '编辑触发器',
    edit_dashboard: '编辑仪表盘',
    edit_database: '编辑数据表',
    edit_database_view: '编辑视图',
    edit_field: '编辑字段',
    edit_form: '编辑表单',
    edit_template: '编辑模板',
    edit_widget: '编辑组件',
    error_import_excel: '导入失败，错误信息：{message}',
    error_import_excel_button: '继续导入',
    export_bika_file_include_data:
      '导出成 bika 文件（.bika），可以将你的文件夹，数据表等资源打包至一起，备份到本地。如果勾选“包含数据表记录”，bika文件的体积会相应增大。',
    export_bika_file_title: '导出 Bika File',
    export_data_include_data: '包含数据表记录',
    export_for_excel: '导出为 Excel',
    features_list: '节点资源类型列表',
    field: '字段',
    field_not_found: '字段未找到',
    fields: '字段',
    filter_condition: '筛选条件',
    first_field_not_allow_drag: '第一个字段不允许拖动',
    folder_description: '文件夹描述',
    folder_empty_description: '这里空空如也，快去创建一些资源吧！',
    folder_empty_title: '空资料夹',
    folder_loading_description: '资源加载中...',
    folder_loading_title: '资源加载中...',
    folder_name: '文件夹名称',
    folder_no_content: '该文件夹内暂无其他文件夹',
    folder_readme: '文件夹说明',
    form: {
      add_logo: '添加 Logo',
      click_to_view: '点击跳转至对应的视图',
      form_description: '请输入描述',
      link_to_resource: '表单的数据存放至：',
      submitted_successfully: '提交成功',
    },
    gallery: {
      cover: '封面图片',
      cover_help_text: '选择附件字段作为封面图片，显示在相册视图中每张卡片的顶部。',
      crop_cover_image: '裁剪封面图片',
      crop_cover_image_help_text: '图片将被裁剪以居中并填充卡片，确保没有空白区域。',
      custom_cards_per_row: '自定义每行的卡片数量',
      custom_cards_per_row_help_text:
        '手动设置每行显示的卡片数量。默认是自动布局（基于屏幕分辨率）。',
    },
    home: '编辑资源',
    import_bika_file_success: '已成功导入',
    import_bika_file_support: '支持上传 .bika 文件',
    import_excel_import_button: '导入',
    import_excel_records_count: '预览仅显示前 10 行数据，实际导入将包含 {count} 行数据',
    import_excel_step1:
      '第一步：下载模板并填写数据。请注意：不要更改模板中的标题，以免导入失败。不支持计算字段、成员字段、附件字段和关联字段。',
    import_excel_step2: '第二步：将填写好的 Excel 文件拖到此处进行上传。',
    import_file_for_box: '从 .bika 文件导入数据并新建资源',
    import_from_excel: '从 Excel 导入',
    import_from_vika: '从 Vika 导入',
    import_from_vika_helper_text:
      '输入Vika资源 ID，多个资源以逗号分隔，例如 fold1、fold2。\n注意：\n1. 首先请确保您已经集成了 VIKA 应用。\n2. 如果表格中有会员字段，请确保先将会员信息从 Vika 平台导入到 Bika，否则导入数据后会员字段会被自动清除。\n3. Vika 用户与 Bika 用户通过邮箱关联。',
    import_from_vika_label: 'Vika 资源 ID',
    include_widgets: '包含组件',
    included_resources: '包含资源',
    incremental_import_from_excel: '从 Excel 增量导入',
    kanban: {
      add_kanban_group_card: '添加记录',
      delete_kanban_tip_content: '删除该分组会将其原有的记录移至未指定分组里',
      delete_kanban_tip_title: '删除这个分组',
      editing_group: '编辑分组',
      group_already_exists: '该分组已经存在',
      group_by_option_or_member: '根据特定的“成员”或“单选”字段将记录分成若干组。',
      hide_kanban_grouping: '隐藏分组',
      kanban_add_new_group: '增加一个分组',
      kanban_no_data: '暂无数据',
      kanban_not_group: '未指定分组',
      kanban_view_limit: '看板视图中最多可以显示1000条记录。请使用“筛选”功能来缩小范围。',
      no_multiple_selection_member_field: '不支持多选的成员字段',
      no_single_choice_or_member_field: '你当前没有任何单选或成员字段，去',
      please_select_single_or_member_field: '请选择一个单选字段或成员字段以创建看板视图',
    },
    layout: '布局',
    layout_help_text: '数据记录在视图中的排列和显示方式。',
    mirror_type_label: {
      database_view: '选择已有视图',
      node_resource: '选择已有资源',
      view: '独立视图',
    },
    month: '月',
    move_resource_error: '移动失败',
    move_resource_success: '移动成功',
    move_resource_to: '移动 {name} 至当前文件夹下',
    move_resource_to_public_description: '确定要移动 "{name}" 至团队资源吗?',
    move_resource_to_public_error: '移动至团队资源失败',
    move_resource_to_public_success: '移动至团队资源成功',
    new_field: '新建字段',
    no_cover: '无封面',
    no_member_field: '没有成员字段',
    no_permission_operation: '您没有权限进行操作',
    no_permission_operation_description: '抱歉，你没有权限进行该操作，请通知管理员',
    node_detail: '节点信息',
    not_support_import_field:
      '从 Excel 导入数据并新建数据表。请注意：不支持计算字段、成员字段、附件字段和关联字段。',
    operation_failed: '操作失败',
    parsing_excel_data: '数据解析中',
    placeholder_no_field: '没有字段',
    placeholder_no_number_field: '没有数字类型字段',
    placeholder_select_field: '请选择一个字段',
    placeholder_select_member_field: '请选择一个成员字段',
    placeholder_select_record: '请选择一条记录',
    placeholder_select_resource: '请选择资源',
    placeholder_select_view: '请选择数据表中的视图',
    placeholder_select_widget: '请选择一个组件',
    placeholder_view_name: '请输入视图名称',
    record_detail: {
      link_new_record: '关联新记录',
      link_record: '关联已有记录',
      linked_from: '从 "{name}" 关联记录',
      no_linkable_record: '没有可关联的记录',
      no_related_records: '暂无关联记录',
      record_detail_not_found: '未找到记录详情',
      select_record: '选择记录',
      tip_refresh: '你正在编辑的内容已发生变化，请复制你编辑的内容到别处保存并刷新页面',
    },
    record_index: '第 {index} 条记录',
    remove_field_success: '删除字段成功',
    remove_folder_description:
      '文件夹及其包含的所有资源将被删除，删除后无法恢复，确定要删除文件夹 "{name}" 吗?',
    remove_shortcut_success: '移除捷径成功',
    required_field: '必填字段',
    resource: '节点资源',
    resources: '资源',
    set_form_required: '设置为必填项',
    set_form_required_description: '当您将字段设置为必填时，用户在创建或编辑记录时必须填写该字段。',
    success: '成功',
    success_import_excel: '已开始导入 {columns} 列字段下的 {rows} 行数据',
    success_import_excel_button: '去查看',
    support_upload_file: '支持上传 .xlsx/.csv 文件',
    template_creator: '模板制作者',
    template_folder_editor: '模板文件夹编辑器',
    title_create_folder: '文件夹',
    title_dashboard_id: '仪表盘',
    title_database_id: '数据表',
    title_delete_resource: '删除资源',
    title_edit_resource: '编辑资源',
    title_export: '导出数据',
    title_field_id: '字段',
    title_form_id: '表单',
    title_form_name: '表单名称',
    title_import: '导入数据',
    title_member_field_id: '成员字段',
    title_mirror_type: '镜像类型',
    title_move_resource_to_public: '移动至团队资源',
    title_move_to: '移动至',
    title_new_ai: 'AI 智能体(Beta)',
    title_new_automation: '自动化',
    title_new_computer: 'New Computer(alpha)',
    title_new_dashboard: '仪表盘',
    title_new_database: '数据表',
    title_new_document: '文档',
    title_new_file: '文件附件',
    title_new_form: '表单',
    title_new_mirror: '镜像',
    title_new_other_resource: '从模板创建',
    title_new_page: 'AI 页面(Beta)',
    title_new_resource: '新建资源',
    title_new_view: '视图',
    title_record_id: '记录',
    title_resource: '资源',
    title_resource_description: '资源描述',
    title_resource_id: '资源',
    title_resource_name: '资源名称',
    title_resource_type: '资源类型',
    title_shortcut: '添加到捷径',
    title_shortcut_cancel: '取消捷径',
    title_shortcut_personal: '添加到个人捷径',
    title_shortcut_personal_remove: '从个人捷径中移除',
    title_shortcut_space: '添加到空间站捷径',
    title_shortcut_space_remove: '从空间站捷径中移除',
    title_show_hidden_field: '显示隐藏',
    title_space_admin: '管理员功能',
    title_space_shortcut: '添加到捷径（所有人可见）',
    title_update_folder: '更新文件夹',
    title_view_id: '视图',
    title_view_type: '视图类型',
    toggle_view: '切换视图',
    type: {
      ai: 'AI 智能体',
      ai_agent: {
        model: 'AI 模型',
        data_source: '数据源',
        data_source_description: '指定智能体在执行任务时可读取的资源，用作上下文参考。',
        description: '输入你的问题，AI 智能体会根据你的需求生成相应的内容和数据',
        prompt_placeholder: '请输入提示词',
        settings_datasource_sitemap_placeholder: '请输入数据源 Sitemap',
        settings_datasource_url_placeholder: '请输入数据源 URL',
        settings_tool_toolsdks_package_key_placeholder: '请输入 Package Key',
        settings_tool_toolsdks_package_version_placeholder: '请输入 Package Version',
        skillset: '技能集',
        skillset_description: '通过技能集扩展智能体的能力，支持接入 MCP 服务器。',
        system_prompt_description: '关于 AI 智能体响应的指示和限制，告诉它如何更好地回答问题。',
        system_prompt_placeholder:
          '向 AI 智能体的指示和限制。\n您可以参考以下写作风格:\n\n# 角色\n您是一位专业的附近旅行顾问，可以为用户提供多种短途旅行和附近旅行计划，并生动描述各种景点的特点。\n\n# 限制\n\n仅讨论与短途旅行和附近旅行相关的内容，拒绝回答无关的话题。\n所有输出必须按照给定格式组织，不得偏离框架要求。\n功能介绍部分不得超过100字。\n',
        title: 'AI 智能体',
        help_doc_link: '/help/guide/ai/ai-agent',
      },
      ai_description: '用于人工智能功能的资源',
      ai_wizard_description:
        'AI 向导是 Bika.ai 中的聊天界面。它可以在平台内用于各种类型和目的的 AI 对话。',
      ai_wizard_features_list: 'AI Wizard List',
      ai_wizard_intent_ui_description:
        '在 Bika.ai 的 AI 向导中，各种类型和目的的 AI 对话被分类，这种分类被称为"意图"。\n        不同的意图会导致不同的操作结果，因为每个意图都被设计用来触发系统内的特定响应或功能。',
      ai_wizard_intent_ui_features_list: 'AI Wizard Intent UI List',
      app_page: 'App Page',
      app_page_description: 'App Page',
      automation: '自动化',
      automation_action_description:
        '自动化动作是指执行任务、活动、事件或变更的步骤，比如发送电子邮件。\n      你可以这样理解动作：当某事发生（触发器）且满足指定条件时，就会执行这个事件（动作）。',
      automation_action_features_list: '执行器列表',
      automation_description: '用于设置和管理自动化流程的资源',
      automation_trigger_description:
        '自动化触发器作为一个"开关"，当满足特定条件时就会启动自动化流程。\n      你可以这样理解触发器：当特定事件发生（触发器）且某些条件成立时，就会执行相应的事件（动作）。',
      automation_trigger_features_list: ' 触发器列表',
      canvas: '画布',
      canvas_description: '用于绘图和设计的画布资源',
      chat: '聊天',
      chat_description:
        '通过 AI 向导，你可以与系统进行自然语言对话，获取所需信息和支持，提升工作效率',
      chat_menu: {
        fold: '不显示',
        pin: '置顶',
        unpin: '取消置顶',
      },
      code_page: 'AI 页面',
      code_page_description: '利用React、Vue去定制的界面',
      create_node_resource: '创建节点资源',
      create_node_resource_description:
        '通过 AI 向导，你可以轻松快速地生成和管理各种节点资源，方便你进行项目的规划和管理。通过这一功能，你可以更有效地组织和分配资源，确保项目的顺利进行。',
      create_record: '创建记录',
      create_record_description:
        '通过 AI 向导，你可以轻松快速地创建新的数据记录，确保信息的准确性和及时性。',
      create_reminder: '创建提醒',
      create_reminder_description:
        '通过 AI 向导，你可以轻松快速地设置各种任务和事件的提醒，确保你不会错过重要的事情。你可以自定义提醒的时间和内容，以满足你的具体需求',
      dashboard: '仪表盘',
      dashboard_description: '用于汇总和展示关键数据的仪表盘',
      database: '数据表',
      database_description:
        '数据表，类似于 Excel 电子表格，但功能更强大。每个数据表包含行和列，行代表记录，列代表字段。你可以在一个文件夹中创建多个数据表，以组织和分类不同类型的数据。数据表支持多种字段类型，如文本、数字、附件、链接等，便于存储多样化信息。你可以通过视图筛选、排序功能，提升数据管理和分析的效率。',
      database_field_description:
        '数据表字段包含数据表中每条记录的详细信息或元数据。\n        数据表字段存储每个数据条目的信息或元数据。这些字段可以采用多种形式，允许数据以文本、单选或多选、图片、复选框、数字、用户标签等形式存储。',
      database_field_features_list: '数据表字段列表',
      database_view_description:
        '数据表视图提供了一种特定的方式来可视化和组织数据表中的基础数据。\n        标准视图是网格形式，但还包括表单、日历、图库和看板等其他布局形式。\n        一个数据表可以支持多个视图和各种类型的视图。',
      database_view_features_list: '数据表视图列表',
      doc: '文档',
      doc_description: '用于创建和存储文档的资源',
      file: '文件',
      file_description: '用于存储和管理附件文件的资源',
      folder: '文件夹',
      folder_description: '用于存储和管理文件的文件夹',
      form: '表单',
      form_description:
        '表单功能让你可以创建自定义的表单，以便收集和输入数据到指定的数据表。你可以通过指定数据表的一个视图，快速生成一个表单，然后分享至各类社交群组。提交的数据会自动更新到相应的表格中，方便管理和分析。表单功能支持文本、附件、复选框等多种字段类型，满足不同数据收集需求。',
      integration_description:
        '集成是 Bika.ai 与外部服务或应用程序之间的连接，可实现平台之间的无缝数据传输。\n        你选择的集成最终取决于你想要用数据解决的具体问题。\n        例如，如果你有一个跟踪任务的数据库记录，并且想要使用 AI 进行总结，你可以利用 OpenAI 集成将数据发送到 OpenAI，然后使用返回的信息发送邮件。',
      mirror: '镜像',
      mirror_description: '用于同步和反映数据的镜像资源',
      report_template: '报告模板',
      report_template_description: '用于创建和管理报告的模板',
      view: '视图',
      view_description: '用于展示和浏览数据的视图资源',
      web_page: '嵌入网页',
      web_page_description: '用于嵌入网页的资源',
    },
    unbind_template_modal_content:
      '脱离模板之后，你可以任意的修改此文件，但无法再获取该模板的后续更新',
    unbind_template_modal_title: '您需要先脱离模板后才能进行该操作',
    update_automation_action_success: '更新执行器成功',
    update_automation_success: '更新自动化流程 "{name}" 成功',
    update_dashboard_success: '更新仪表盘 "{name}" 成功',
    update_database_success: '更新数据表 "{name}" 成功',
    update_folder_success: '更新文件夹 "{name}" 成功',
    update_view_success: '更新视图 "{name}" 成功',
    view_count: '{count} 个视图',
    view_hidden_all_field: '隐藏所有列',
    view_hidden_field: '隐藏列',
    view_name: '视图名称',
    view_show_all_field: '显示所有列',
    view_show_field: '显示列',
    view_type: {
      form: '表单',
      gantt: '甘特图',
      grid: '表格',
      kanban: '看板',
    },
    views: '视图',
    week: '周',
    widget_setting: '配置组件',
    widgets: {
      description:
        '组件是一种用于展示数据的可视化工具，可以在仪表盘中添加多个组件，以便快速查看和分析数据。组件支持多种类型，如表格、图表、计数器、进度条等，满足不同数据展示需求。',
      features_list: '仪表盘组件列表',
      name: '组件',
    },
  },
  role: {
    allow: '允许',
    msg_create_role_success: '角色创建成功',
    msg_delete_role_error: '删除{name}成功，{message}',
    msg_delete_role_success: '删除{name}成功',
    msg_load_role_error: '加载角色信息失败',
    msg_update_role_success: '角色更新成功',
    role: '角色',
    roles: '角色',
    select_role: '选择角色',
  },
  scheduler: {
    daily_base: '按天',
    friday: '星期五',
    hourly_base: '按小时',
    last_day: '最后 1 天',
    minute_base: '按分钟',
    monday: '星期一',
    monthly_base: '按月',
    repeat_frequency: '重复频率',
    repeat_interval: '重复间隔',
    repeat_per_monthday: '每月重复',
    repeat_per_weekday: '每周重复',
    saturday: '星期六',
    started_at: '开始时间',
    sunday: '星期日',
    thursday: '星期四',
    timezone: '时区',
    tuesday: '星期二',
    wednesday: '星期三',
    weekly_base: '按周',
    yearly_base: '按年',
  },
  settings: {
    about: {
      about_brand: 'Bika 版本',
      help_center: '帮助中心',
    },
    account: {
      account: '账号',
      account_information: '账号信息',
      api: '开发者',
      api_description: '请妥善保管你的开发者API Token！',
      connected_account: '账号绑定与安全',
      connected_account_description: '绑定下方平台的帐号，实现快捷登录 Bika。',
      login_record_description:
        '记录了您在系统中的登录活动信息，以帮助保护您的账户安全并提供更好的用户体验。',
      notification: '通知',
      referral: '推荐奖积分',
      session_logs: '会话日志',
      top_up: '充值',
      unbind: '解绑',
      unbind_description: '解绑后，您将无法使用此方式登录 Bika。',
    },
    audit: {
      action: '动作',
      action_name: {
        invitation_email_accept: '接受邀请',
        invitation_email_delete: '邀请邮件',
        invitation_email_resend: '邀请邮件',
        invitation_email_send: '邀请邮件',
        invitation_link_accept: '邀请链接',
        invitation_link_create: '邀请链接',
        invitation_link_delete: '邀请链接',
        node_create: '创建资源',
        node_delete: '删除资源',
        node_detach: '模板脱离',
        node_export: '导出资源',
        node_get: '访问资源',
        node_import: '导入资源',
        node_publish: '发布资源',
        node_update: '更新资源',
        node_upgrade: '模板升级',
        share_grant: '授权资源',
        share_password_create: '分享加密',
        share_password_delete: '分享加密',
        share_password_update: '分享加密',
        share_restore: '恢复资源',
        share_revoke: '撤销资源',
        share_scope_update: '分享范围',
        share_upsert: '分享资源',
        space_update: '更新空间',
        template_install: '安装模板',
      },
      actor: '操作人',
      description: '描述',
      empty: '暂无',
      page_info: '显示第 1 到 {size} 条，共 {total} 条事件',
      search_empty: '未找到',
      template: {
        invitation_email_accept: '接受邀请 {email}',
        invitation_email_delete: '删除邀请 {email}',
        invitation_email_resend: '重发邀请 {email}',
        invitation_email_send: '邀请 {emails}',
        invitation_link_accept: "接受<a href='/space/join/{token}'>邀请链接</a>",
        invitation_link_create: "创建 <a href='/space/join/{token}'>邀请链接</a>",
        invitation_link_delete: "删除 <a href='/space/join/{token}'>邀请链接</a>",
        node_create: "在 {parent} 下创建资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_delete: '删除了资源 {name}',
        node_detach: "分离了资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_export: "导出了资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_get: "访问资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_import: "导入了资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_publish: "发布了资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_update: "更新了资源 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_upgrade: "升级模板文件夹 <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        share_grant: '授予 {units} 对资源 {name} 的访问权限',
        share_password_create: '为访问共享资源 {name} 创建了密码',
        share_password_delete: '删除访问共享资源 {name} 的密码',
        share_password_update: '更新访问共享资源 {name} 的密码',
        share_restore: '恢复对资源 {name} 的访问权限',
        share_revoke: '撤销 {unit} 对资源 {name} 的访问权限',
        share_scope_update: '更新资源 {name} 的共享范围',
        share_upsert: '创建或更新资源 {name} 的分享链接',
        space_update: '更新了空间站配置',
        template_install: "安装了模板 (<a href='/space/{spaceid}/node/{nodeid}'>{name}</a>)",
      },
      time: '时间',
    },
    billing: {
      already_support: '已解锁',
      apply_refund: '申请退款',
      cancel_subscribe: '取消订阅',
      cancel_subscription_content: '你确定要取消订阅吗？',
      cancel_subscription_tips: '取消成功，您仍可在当前账单周期结束前享有所有功能的完整访问权限',
      cancel_subscription_title: '取消订阅',
      canceled_at: '取消时间',
      change_payment_method: '更改付款方式',
      change_plan: '更改计划',
      current_plan: '当前计划',
      manage: '管理账单',
      next_invoice_date: '下次账单日期',
      no_billing_day: '无',
      not_support: '不支持',
      payment_status: {
        buying_plan: '您正在购买{plan}计划',
        guest_limit: '🌟 1000 guests',
        payment_failed: 'Payment failed',
        payment_failed_description: 'Sorry, subscription to {plan} plan failed, please try again',
        privileges_gained: 'You have gained the following privileges:',
        storage_limit: '🌟 200GB storage space',
        try_again: 'Try again',
        try_now: 'Try now',
        unlimited_members: '🌟 Unlimited members',
        upgrade_success: 'Congratulations on upgrading to {plan} successfully!',
        waiting_payment: '等待付款',
      },
      resume_subscription: 'Resume Subscription',
      start_now: '立即使用',
      usage: '空间站用量',
      usage_detail: '用量详情',
      usage_limit_template:
        '抱歉, 该空间站的<strong>{feature}</strong>上限值为<strong>{max}</strong>, 截止当前已使用数为<strong>{current}</strong>, 请升级计划即可解锁更多上限',
      usage_tips: '如果需要购买帮助可以联系我们',
      usages: {
        api_request: '每月 API 请求次数',
        automation_runs: '每月自动化运行次数',
        emails: '每月邮件发送量',
        guests: '访客数',
        missions: '每月待办任务总量',
        records: '空间站记录总数',
        reports: '每月报告总量',
        resource_permission: '可分配权限资源数',
        resources: '节点资源数',
        seats: '席位数',
        space_integrations: '集成实例',
        storages: '存储容量',
        unused: '剩余',
        used: '已用',
      },
      wating_payment: '等待付款',
      will_cancel_subscribe: '您的订阅将在 {expireAt} 前保持有效，之后将自动降级为免费方案。',
      you_will_lose_all_privileges: '取消订阅后您将失去所有特权',
    },
    coin_rewards: {
      all_plan: '所有计划',
      all_plan_description: '如果您降级或取消订阅，退款不会自动处理。请',
      and: '和',
      choose_plan: '选择计划',
      coin_description: '可用于',
      copy_invitation_code: '复制邀请码',
      copy_invitation_link: '复制邀请链接',
      current_plan: '当前计划',
      get_coin_history: '奖励记录',
      get_rewards_methos_1: '方式一：通过邀请链接邀请新用户时，双方都可获得',
      get_rewards_methos_2: '方式二：将您的邀请码提供给未填写邀请码的用户，双方都可获得',
      get_rewards_methos_3: '方式三：邀请成员进入空间站，双方都可获得',
      get_rewards_methos_4: '方式四：安装 App，您将获得',
      invite_member_rewards_coin: '邀请成员送积分',
      no_coin_history: '暂无奖励记录',
      plan_unit: '每个成员/月',
      purchase_advanced_mode: '购买高级模式',
      recommend_get_bika_coin: '推荐奖积分',
      to_invite_member: '去邀请成员',
      upgrade_space: '升级空间站',
      view_usage: '查看用量',
      your_coin: '你的积分',
    },
    general: '通用',
    guest: {
      create_guest_team: '创建访客组',
      delete_guest_team_description: '访客组删除后将无法恢复，确定要删除此访客组吗?',
      delete_guest_team_success: '删除访客组成功',
      delete_guest_team_title: '删除访客组',
      edit_guest_team: '编辑访客组',
      no_guest: '暂无访客',
      select_guest: '选择访客',
      select_guest_team: '选择访客组',
    },
    member: {
      assign_members: '分配成员',
      choose_member_role: '选择成员角色（可选）',
      choose_member_role_placeholder: '请选择角色',
      choose_member_team: '设置小组',
      choose_member_team_placeholder: '请选择小组',
      delete_member: '删除成员',
      delete_member_confirm: '确定将成员移出空间站？',
      description: '描述',
      edit_error: '编辑失败',
      edit_member: '编辑成员',
      edit_success: '编辑成功',
      empty_email: '暂无邮箱',
      member_name_setting_description:
        '成员名是在空间站内部使用的昵称，它在不同的空间站之间不会改变',
      member_or_group_not_found: '未查找到成员或小组',
      remove_member: '移除成员',
      remove_member_confirm: '确定要将所选成员移出该角色吗？',
      remove_member_confirm_content: '所选成员可能将会失去部分权限，确定要将所选成员移出该角色吗？',
      remove_members: '移除 {count} 个成员',
      set_space_member_name: '设置空间站内的昵称',
    },
    message_member_name_modified_failed: '站内昵称修改失败',
    message_member_name_modified_successfully: '站内昵称修改成功',
    nickname_modified_failed: '昵称修改失败',
    notification: {
      advertise: '建议下载 Bika.ai 的手机客户端,获得更及时的提醒和专属体验',
      description:
        'Bika 支持将任务、日程、报告的最新情况，通过多种通知方式给你发送提醒消息，让你可以更实时地掌握各种动态',
      email: {
        description: '绑定后可通过邮件接收通知',
        title: '接收邮件通知',
      },
      push: {
        description: '绑定后可通过浏览器接收通知',
        title: '接收浏览器通知',
      },
      sms: {
        description: '绑定后可通过短信接收通知',
        title: '接收短信通知',
      },
      title: '通知集成',
      wechat_push: {
        setting: '设置',
        title: '微信公众号通知',
      },
    },
    other: '其他',
    role: {
      confirm_delete_role: '确定要删除该角色吗？',
      create_role: '创建角色',
      create_role_empty_error: '角色名称不能为空',
      create_role_placeholder: '请输入角色名称',
      delete_role: '删除角色',
      delete_role_confirm_content: '删除角色不可恢复，确定要删除吗',
      delete_roles: '删除 {count} 个角色',
      deselect: '取消选择',
      edit_role: '编辑角色',
      edit_role_error: '无法编辑, 请重新刷新页面',
      edit_role_placeholder: '请输入角色名称',
      management_role: '管理角色',
      member_no_role_tips: '该角色暂无成员',
      need_select_a_role: '请选择一个角色',
      need_select_role: '请选择角色',
      non_management_role: '非管理角色',
      role_name: '角色名称',
      role_permission: '角色权限',
      role_type: '角色类型',
      select: '选择',
    },
    space: {
      announcement_description:
        '公告内容将出现在空间站首页，所有成员进入空间站即可看到，同时，支持 Markdown 格式进行编辑，以提升公告的可读性和美观性。',
      announcement_placeholder: '请输入公告内容，让成员了解最新动态',
      authorized_domain: '授权邮箱域名',
      authorized_domain_description:
        '设置授权邮箱域名以限制访问空间站。只有来自这些域名的邮箱帐号可以加入，其他将被拒绝。',
      authorized_domain_settings: '授权邮箱域名设置',
      config_watermark: '水印',
      create_and_go: '创建并前往',
      create_mission: '创建智能任务',
      create_mission_description:
        '您可以通过设置自动化触发不同类型的新任务，支持设置任务的执行人、节点资源、截止日期等详细信息。任务创建后，将根据设定的时间与资源自动触发，并通过AI进行任务管理和跟踪，确保高效完成任务。',
      create_space_description: '想给它起什么名字呢？',
      current_permission: '当前空间站默认的权限是',
      current_permission_tips:
        '这意味着当任何人创建一个新资源并且没有分配权限时，空间站中的所有成员将默认拥有',
      default_space_name: '我的空间站',
      delete_space: '删除空间站',
      delete_space_confirm_desc:
        '此操作不可撤销！为避免误操作，请输入空间站 ID {spaceId} 进行确认。删除后，所有数据将永久丢失。',
      delete_space_confirm_title: '删除空间站',
      delete_space_desc:
        '删除空间站后，空间站内的所有数据（包括节点资源、附件等）将被永久清除且无法恢复',
      delete_space_success: '删除空间站成功',
      description:
        '空间站是 Bika.ai 的核心功能，是您的工作空间，您可以在空间站中创建、管理、协作各种资源，实现团队协作。',
      disable_global_manage_resource: '控制成员在根节点下的资源创建、访问和操作权限',
      disable_global_manage_resource_description:
        '控制成员加入空间站后，对节点资源的初始访问权限。不开启，则全体成员默认拥有「可管理」的权限。',
      disable_global_manage_resource_learn_more: '跳转到帮助文档了解更多',
      invite_members: '邀请成员',
      ip_address_placeholder: '请输入 IP 地址',
      join: '加入',
      member_management: '成员管理',
      member_management_description:
        '成员管理旨在帮助您高效地管理团队成员。通过该模块，您可以添加、删除和编辑成员信息，并为每个成员分配适当的角色和权限。成员管理模块还提供了搜索和筛选功能，方便您快速找到特定成员。此外，您还可以创建和管理小组，将成员添加到小组中，以便进行更细致的组织架构管理。',
      mission: '任务',
      mission_description:
        '由AI分配的特定任务或目标，通常是自动化工作流程的一部分。在 Bika.ai 中，根据您的设置AI会自动生成任务提示您去完成。',
      permission_settings_description: '空间站资源管理',
      placeholder_authorized_domain: '请输入授权邮箱域名，如：example.com',
      please_input_space_id: '请输入空间站 ID',
      request_new_record: '请求记录',
      request_new_record_description:
        '系统中提交的请求日志，包括时间戳和状态等详细信息。Bika.ai 使用它来跟踪自动化任务和用户输入以进行报告。',
      rich_document: '富文本',
      rich_document_description:
        '一种支持文本、图像和其他媒体元素的文本格式，提供增强的内容展示。Bika.ai 允许集成各种文档类型，用于报告和自动化。',
      role_management: '角色管理',
      role_management_description:
        '角色管理旨在帮助您高效地管理和分配权限。在安装模板时，部分模板会预设一些角色，您可以将成员添加到这些角色中，以便更好地完成模板配置。此外，您还可以自由创建自定义角色，并将任意部门或成员分配到一个或多个角色中，从而实现更灵活的权限控制。通过角色管理，您可以确保每个成员拥有适当的权限，提升团队协作效率。',
      setting_info: '设置信息',
      setting_info_description:
        '设置信息是您在点开个人设置后弹出的设置界面，可以在此更新个人资料、选择主题、设置时区及配置系统语言。该界面帮助用户自定义偏好，以获得更加个性化的体验。',
      space: '空间站',
      space_audits: '空间站审计',
      space_create_failed: '创建空间站失败，请重试',
      space_created_success: '空间站创建成功！',
      space_has_be_deleted: '空间站已被删除',
      space_has_be_deleted_desc: '抱歉，你没有权限查看，请通知管理员',
      space_name_required: '请输入空间站名称',
      space_settings: '空间站设置',
      space_sidebar: '空间站侧边栏',
      space_sidebar_description:
        '空间站侧边栏提供快速导航，帮助您轻松访问空间站中的各项功能和模块。通过侧边栏，您可以查看主页、任务、报告、资源、设置等内容，简化操作流程，提升工作效率。侧边栏的设计旨在为您提供简洁的操作体验，使团队协作更加流畅。',
      third_party_integration: '第三方集成',
      upgrade: '付费升级',
      usage: '用量 & 账单',
      wallpaper_button: '设置壁纸',
      wallpaper_description: '壁纸将出现在空间站首页，所有成员进入空间站即可看到。',
      wallpaper_preset_photos: '预设壁纸',
      wallpaper_title: '主页壁纸',
      watermark_description: '全局水印',
      watermark_description_2:
        '为了保证企业信息安全，资源支持显示全局水印。水印内容为当前访问成员的姓名+手机号后缀或者邮箱前缀，防止截屏泄密',
      workflow: '工作流',
      workflow_description:
        '一系列任务或流程，旨在实现特定的结果。在Bika.ai中，工作流程由AI自动化管理，简化了跨不同功能的重复任务。',
    },
    upgrade: {
      action_record: '详情',
      ai_invoke_consume: 'AI 调用消耗',
      ai_invoke_count_达标奖励: 'AI 调用次数达标奖励',
      benefit_details: '查看权益详情',
      bkc: '使用 BKC 抵扣',
      bkc_deduction: 'BKC 抵扣',
      cancel_subscription: '取消订阅',
      consumption_log: '使用情况',
      credit: '空间站积分',
      credit_desc:
        '空间站积分是空间站用户通过订阅、邀请成员、消费积分等方式获得的积分，可以用于兑换空间站服务。',
      currency: '货币种类',
      currently_owns: '当前拥有',
      cycle: '订阅周期',
      cycle_descritpion: '计划每月自动续订，直至取消。',
      date: '日期',
      detail: '详情',
      get_more_bika: '获得更多积分',
      gift_credit: '每日',
      gift_credit_desc: '由官方每日赠送的免费积分，登录即可获得，每日自动刷新。',
      invite_logup: '邀请注册',
      invite_member: '邀请成员',
      invite_people: '邀请好友',
      invite_space: '邀请加入空间站',
      loading_tips: '正在计算价格，请稍等～',
      member: '成员',
      or: '或',
      other_method_bkc: '还可以通过',
      pay_annual: '年付',
      pay_monthly: '月付',
      pay_now: '支付',
      pay_tips: '继续，即表示同意 Bika.ai 条款和条件。',
      payment: '选择付费方式',
      permanent_credit: '赠送',
      permanent_credit_desc: '通过官方活动以及邀请成员等方式获得的积分',
      plan: '订阅计划',
      quanty: '席位数',
      recharge_consumption_integral: '积分变更',
      resume_subscription: '恢复订阅',
      serial_number: '序号',
      space: '空间站名称',
      space_member_num: '空间站成员数量',
      subscribe_credit: '订阅',
      subscribe_credit_desc:
        '由空间站的订阅计划决定，订阅后可获得相应的空间站积分，并按照订阅日期每月自动刷新。',
      subtotal: '小计',
      total: '合计',
      unit_price: '单价',
      user_referral: '用户推荐',
      user_topup: '用户充值到空间站',
    },
  },
  share: {
    already_set_permission: '已限制权限，不再继承父级文件夹的权限',
    change_permission_success: '权限修改成功',
    change_pwd: '修改密码',
    change_pwd_success: '已修改密码',
    change_share_pwd: '修改分享密码',
    close_short_link_warning: '关闭短链接后，下方的短链接将失效',
    copy_pwd_and_link: '复制链接和密码',
    copy_pwd_and_link_success: '已复制链接和密码',
    create_short_link_warning: '创建一条短链接，下方的分享链接将失效',
    has_num_member_share: '有 {memberCount} 位成员共享中',
    member_of_share_permission: '共享权限的成员',
    network_user_need_pwd: '互联网上的用户需要密码访问，组织内用户无需密码可直接访问',
    open_pwd: '启用密码',
    permission: {
      input_pwd: '输入密码',
      no_login_visit: '你可能没有权限或者没有登录导致的',
      no_permission_visit: '你没有权限查看该资源',
      no_pwd_visit: '因为该资源设置了密码，你需要输入密码',
      notify_admin: '通知管理员',
      notify_admin_for_permision: '抱歉，你没有权限查看，请通知管理员',
      notify_admin_success: '已经通知！请耐心等候！',
      publish_to_the_community: '发布到社区',
      replay_mode: '重播模式',
      right_pwd: '密码正确',
      share_permission_can_edit: '互联网获得链接的人可编辑',
      share_permission_can_view: '互联网获得链接的人可查看',
      share_permission_default: '仅限空间站内指定的成员或访客访问',
      share_permission_form_anonymous_write: '链接访问者无需登录即可匿名提交表单',
      share_permission_form_login_write: '链接访问者需登录后才能提交表单',
      wrong_pwd: '密码错误',
    },
    pwd_pattern: '请输入新密码',
    recover: '恢复',
    recover_permission_success: '恢复权限成功',
    set_share_pwd: '设置分享密码',
    share: '分享与权限',
    share_text: '分享',
    ai_conversation: 'AI 对话',
  },
  shortcuts: {
    no_pin_so_far: '暂无捷径，快把常用文件设为捷径',
    pin_to_top: '置顶',
    pinned: '已置顶',
    shortcuts: '捷径',
    unpin_from_top: '取消置顶',
  },
  skillset: {
    page_description:
      'Bika.ai 技能集包括 MCP 服务器、第三方应用程序，以及可用于您的 AI 代理和自动化工作流程的集成功能。',
    page_title: '技能集、应用程序、集成功能',
    bika_document: {
      create_document: '创建文档',
    },
    bika_search: {
      bika_search_pages: 'Bika 搜索网页',
      bika_search_images: 'Bika 搜索图片',
    },
    bika_research: {
      bika_company_research: 'Bika 公司调研',
    },
    bika_office: {
      generate_markdown_document: '生成 Markdown 文档',
      generate_slides: '生成幻灯片',
    },
    bika_database: {
      list_records: '列出记录',
      aggregate_records: '聚合记录',
      create_record: '创建记录',
      get_database_detail: '获取数据库详情',
      get_fields_schema: '获取字段 schema',
    },
    bika_automation: {
      run_automation: '运行自动化',
      get_automation_detail: '获取自动化详情',
    },
    twitter: {
      send_direct_message: '发送私信',
      reply_tweet: '回复推文',
      create_tweet: '创建推文',
      search_recent_tweets: '搜索近期推文',
    },
    custom_mcp_server: '自定义 MCP 服务器',
    bika_datasets: {
      companies: '公司',
      people: '人员',
    },
    bika_space: {
      search_everything: '搜索所有内容',
      get_node_info: '获取节点信息',
      list_nodes: '列出节点',
      list_members: '列出成员',
      list_teams: '列出团队',
      list_roles: '列出角色',
      list_users: '列出用户',
    },
    bika_ai_page: {
      generate_html_page: '生成 HTML 页面',
      ask_for_apply_ai_page: '申请 AI 页面咨询',
    },
    bika_super_agent: {
      search_launcher_commands: '搜索启动器命令',
      search_node_resources: '搜索节点资源',
      search_help_center: '搜索帮助中心',
      search_members_users: '搜索成员/用户',
      search_database_records: '搜索数据库记录',
      search_documents: '搜索文档',
    },
    bika_email: {
      send_email: '发送邮件',
    },
    bika_image: {
      generate_image: '生成图片',
    },
    everything_mcp_server: {
      add: '添加',
      echo: '回声',
      print_env: '打印环境',
      sample_llm: '示例大语言模型',
      get_tiny_image: '获取小图片',
      annotated_message: '带注释的消息',
      get_resource_links: '获取资源链接',
      start_elicitation: '开始启发',
      structured_content: '结构化内容',
      get_resource_references: '获取资源引用',
      long_running_operation: '长时间运行的操作',
    },
    fetch_mcp_server: {
      fetch: '获取',
    },
  },
  slogan: {
    alternatives: [
      {
        name: 'vika维格表(维格云)',
        url: 'https://vika.cn/',
        description:
          '与vika维格表相比，Bika.ai 更专注于 AI 自动化和主动式协助。Bika.ai 更适合需要更多自动化和 AI 协助的用户。',
      },
      {
        name: '飞书多维表格',
        url: 'https://www.feishu.cn/product/base',
        description:
          '与飞书多维表格相比，Bika.ai 更专注于模板和数据库工作流。Bika.ai 更适合需要更多自动化和 AI 协助的用户。',
      },
      {
        name: 'Make',
        url: 'https://www.make.com/',
        description:
          'Compared to Make, Bika.ai provides more integrated AI-driven solutions and proactive automation directly within its platform. Bika.ai is ideal for users seeking deep automation with advanced AI capabilities to streamline complex workflows and data management tasks.',
      },
    ],
    highlights: [
      {
        icon: '/assets/icons/highlights/auto-template.png',
        name: 'AI员工们的钉钉/企微',
        description: '像聊微信一样，协作、构建和管理一个智能化AI组织。',
        keywords: 'AI员工, AI 智能体, 智能组织, 无代码, 协作平台',
      },
      {
        icon: '/assets/icons/highlights/ai-automation.png',
        name: 'MCP驱动的应用集成平台',
        description:
          '可连接或自定义过万个MCP工具。预设的技能工具包括搜索（网页、图片）、研究和办公工具（幻灯片、文档、电子表格）等。',
        keywords: '应用集成, 工具连接, 自动化工作流, MCP工具',
      },
      {
        icon: '/assets/icons/highlights/data-visual.png',
        name: '超强无代码工作站',
        description:
          '丰富的无代码组件，亿级大数据的多维表格、自动化工作流程、实时协作文档、仪表板等尽在一处。兼容OpenAPI且可扩展。',
        keywords: '无代码, 工作站, 多维表格, 自动化工作流, 实时协作',
      },
      {
        icon: '/assets/icons/highlights/auto-publish.png',
        name: 'AI智能体商店',
        description: '制作并发布您自己的智能化 AI 模板和 AI 员工，并与社区分享。',
        keywords: 'AI智能体, 模板商店, 社区分享, 代理AI',
      },
    ],
    keywords:
      'AI 组织者, Vika, 多维表格, Manus, Genspark, AI 协同平台, 无代码 AI 自动化, 智能 AI 团队构建, AI 营销自动化, AI 线索管理, Airtable 替代方案, Zapier 替代方案',
    personas: [
      {
        name: '市场营销人员',
        description: '市场营销人员',
      },
      {
        name: 'KOL内容创作者',
        description: 'KOL内容创作者',
      },
      {
        name: '自动化顾问',
        description: '自动化顾问',
      },
      {
        name: '项目管理经理',
        description: '项目管理经理',
      },
      {
        name: '销售负责人',
        description: '销售负责人',
      },
    ],
    screenshots: ['/assets/blog/what-is-bika-ai/template.en.gif'],
    slogan_mkt_e:
      'Bika.ai是一个企业AI智能体平台，结合了大数据多维表格和AI自动化，提供AI Agent增强的客户管理系统、营销自动化系统、项目管理系统、BI和ERP，一切皆以惊人的价格获得。',
    slogan_mkt_s: '打造您的 AI 智能体员工团队',
    slogan_prd_e:
      'Bika.ai是一个企业AI智能体平台，融合了大数据多维表格和自动化连接器的功能，提供AI Agent增强的客户管理系统、营销自动化系统、项目管理系统、BI和ERP，一切皆以惊人的价格获得。',
    slogan_prd_l:
      'Bika.ai 可以像微信聊天一样，管理一支 AI 团队，用于创造支持 MCP 的 AI 数字员工伙伴 - 与 AI 智能体、文档、大数据多维表格、自动化流程、表单、仪表盘聊天，实现无限的自动化业务场景。',
    slogan_prd_m: '企业AI智能体平台，让AI积极主动地完成各种工作',
    slogan_prd_xl:
      'Bika.ai是一个企业AI智能体平台，融合无代码、多维表格、数据中台、企业级AI知识库，让AI积极主动地完成销售自动化、营销自动化、项目管理AI化。无需与AI不断对话，Bika.ai可以自动化重复任务，让您专注于战略性工作。',
    slogan_title: 'Bika.ai: AI 智能组织平台, 打造您的 AI 智能体员工团队',
    use_cases: [
      {
        name: '营销自动化',
        description:
          '帮您批量、定时、间隔地自动发送电子邮件、企业微信/钉钉/飞书通知、小红书/知乎文章、通知短信等内容,实现快速高效的营销自动化',
      },
      {
        name: '销售线索管理',
        description:
          '自动收集、跟踪和管理上百万条销售线索,帮助您系统化地跟进潜在客户,提高销售转化率',
      },
      {
        name: 'AI向你汇报',
        description: '定期主动找你建议AI策略和自动化流程,在您决策后才执行,AI还会定期向你生成汇报',
      },
      {
        name: '一站式解决',
        description:
          '无需复杂的专业软件,Bika.ai轻量级的AI自动化数据库,可满足您的客户数据储存、管理和跟踪需求',
      },
      {
        name: '自定义编辑',
        description:
          'Bika.ai提供强大的低代码/无代码编辑器,让您轻松定制各种自动化任务流程和数据系统,实现项目管理、产品工单、订单管理等更多应用场景',
      },
    ],
    usp: 'Bika.ai 提供了一个开箱即用的自动化数据库，内置丰富的功能和第三方集成。不管数据量有多大，哪怕是数十亿条数据，Bika.ai 都能轻松应对。使用 Bika.ai，您无需不断与 AI 对话，数据量也不再是问题。\n\n通过 Bika.ai 自动完成任务，工作更高效精确，节省大量时间。用户还能轻松发布、分享和复制自动化模板，便于持续改进。如果你想让市场营销、销售或项目管理更简单，同时用 AI 自动化来提升数据处理能力，Bika.ai 就是你的理想选择。',
    video_ai_agent: 'https://www.youtube.com/embed/POLa4KmVtVo?si=EdkgoshBfbHbCmML',
    video_automation: 'https://www.youtube.com/embed/g0WOF2hkSH0?si=k_hZ-m0BDdcyuSVg',
    video_dashboard: 'https://www.youtube.com/embed/VsrUHkjbbbU?si=1K6XO_liycfxNyyc',
    video_database: 'https://www.youtube.com/embed/BdP9qskz89s?si=KbWMCzSFsu9OQzVG',
    video_documents: 'https://www.youtube.com/embed/XuWV2nSvvoA?si=vYmwS-JUxduCAJMd',
    video_forms: 'https://www.youtube.com/embed/Wi6scmIzDKE?si=ggjeyXaTI2KGjPbw',
    video_introduction: 'https://www.youtube.com/embed/t_f0ZI1VpY4?si=iPrELXmvnZkEPJA6',
    video_onboarding:
      'https://player.bilibili.com/player.html?isOutside=true&aid=112556530208615&bvid=BV1T6TDePExz&cid=26754483562&p=1&autoplay=0',
    video_partners: 'https://www.youtube.com/embed/CxJFssdj6hs?si=uE-30GbTyRCMM4kP',
    video_product:
      'https://player.bilibili.com/player.html?isOutside=true&aid=113621531100625&bvid=BV1wPqPY7EJG&cid=27252887234&p=1&autoplay=0',
  },
  sort: {
    sort_setting: '排序设置',
    sort_title: '排序',
  },
  space: {
    advanced: '高级',
    all_members: '我的小组成员(包含子小组)',
    announcement: '空间站首页公告',
    default_space_name: '我的空间站',
    email_domain: '电子邮件域',
    enter_announcement: '输入公告',
    features_list: 'Space Features List',
    goto_space: '进入我的空间站',
    group_members: '我的小组成员(不包含子小组)',
    home: {
      installed_templates: '已安装的模板应用',
      invite_members: '邀请好友加入',
      set_space_announcement: '设置空间站公告',
      space_announcement: '公告',
      view_help_document: '查看帮助文档',
      view_templates: '看看有什么模板',
    },
    import: '导入数据',
    import_description: '可以直接将本地文件导入到 Bika 空间站内',
    integration: '集成',
    integrations: '集成',
    members: '成员',
    members_and_teams: '成员和小组',
    msg_go_to_space_settings: '编辑修改',
    msg_space_name_modified_success: '名字修改成功，改成了: {spaceName}',
    new_space: '新的空间站',
    no_data: '无数据',
    no_name: '未命名',
    no_permission_content: '请确保链接正确且您有访问权限。如有任何疑问,请联系任务发布者。',
    no_permission_title: '无权查看此任务',
    preview_import: '预览当前导入的数据',
    removal_from_space: '从空间站移除',
    removal_from_space_description: '确定要从空间站移除此成员吗?',
    role: '角色',
    show_watermark: '显示水印',
    space: '空间',
    space_creator: '创建人',
    space_domain: '空间域名',
    space_logo: '空间 Logo',
    space_name: '空间名称',
    space_settings: '空间设置',
    space_subdomain: '空间子域名',
    teams: '小组',
    unnamed: '未命名空间站',
    watermark: '水印',
    you_will_be_assigned_a_subdomain: '您将被分配一个子域名:',
  },
  tags: {
    completed: '已完成',
    due: '已过期',
    invalid: '失效',
    pending: '进行中',
    read: '已读',
    rejected: '已拒绝',
    request_changed: '请求修改',
    review: '审批中',
    unread: '未读',
  },
  task: {
    cutoff_time: '截止时间',
    task: '任务',
  },
  team: {
    create_team: '创建小组',
    delete_team: '删除小组',
    delete_team_description: '小组删除后将无法恢复，确定要删除此小组吗?',
    delete_teams: '删除 {count} 个小组',
    edit_team: '编辑小组名称',
    join_team_description: '通过此链接加入的小组成员将自动分配到群组和角色中。',
    menu_remove_member_from_space: '移出空间站',
    menu_remove_member_from_team: '移出小组',
    msg_add_member_success: '成员已成功加入小组',
    msg_create_team_success: '小组创建成功',
    msg_delete_team_error: '删除小组失败',
    msg_delete_team_success: '删除小组成功',
    msg_remove_member_from_space_success: '已将成员从空间站移除',
    msg_remove_member_from_team_success: '已将成员从小组移出',
    msg_rename_team_success: '小组重命名成功',
    msg_team_name_not_empty: '请输入小组名称',
    placeholder_new_team: '请输入小组名称',
    placeholder_select_members: '请选择要加入当前小组的成员',
    remove_index_members: '移除 {{index}} 位成员',
    remove_member_from_space: '将成员从空间站移除',
    remove_member_from_space_description:
      '成员将被移除，但他们的历史、评论、上传、任务和其他所有内容将保留，不会被删除。被移除的成员仍可能出现在搜索和筛选中。',
    remove_member_from_team: '将成员移出小组',
    remove_member_from_team_description: '确定要将此成员移出小组吗?',
    select_team: '选择小組',
    show_ai: '显示AI',
    show_all: '显示全部',
    show_member: '显示成员',
    team: '小组',
    teams: '小组',
    unselect_all: '取消全选',
  },
  template: {
    ai_create: 'AI 应用顾问',
    architecture: '流程图',
    architecture_description: '{name}的流程图',
    change_log: '更新日志',
    check_original_template: '查看原始模板',
    coming_soon: '敬请期待',
    coming_soon_tooltip: '模板制作中，敬请期待',
    comments: '评论',
    delete_template: '删除模板',
    delete_template_description: '确定删除模板 「{name}」 吗?',
    delete_template_success: '删除模板 "{name}" 成功',
    empty_change_log: '暂无更新日志',
    export: '导出',
    favorite: '收藏',
    feedback_email: '您的邮箱',
    feedback_email_placeholder: '请输入你的邮箱',
    feedback_ideas: '您的想法是什么？',
    feedback_placeholder: '请输入您的想法或建议',
    feedback_thanks: '感谢您的反馈',
    get: '安装',
    install: '安装',
    install_template: '安装模板',
    install_toast: '模板安装成功',
    make_it_faster: '让工作更快',
    no_readme: '作者还没有写说明书呢',
    no_template: '当前分类下还没有模板',
    not_found_template: '找不到想要的模板？请告诉我们',
    official_certification: '官方认证',
    open: '打开',
    read_more: '→ 了解更多 Bika.ai 是什么',
    readme: '说明',
    release_notes: '版本更新日志',
    release_notes_description: '{name}的版本更新日志',
    releases_history: '发布历史',
    select_one_space: '请选择一个空间站',
    select_space: '选择空间站',
    star_success: '收藏成功',
    template: 'AI自动化应用',
    title: '模板应用',
    try_other_templates: '尝试其他模板',
    unstar_success: '取消收藏成功',
    upgrade: '升级',
    website_description:
      '找到<%= category %>领域功能强大的企业AI智能体和自动化工作流程。让AI为您工作，节省时间并优化结果。',
    website_title:
      '<%= category %> 领域最佳的<%= count %>个商业AI智能体模板与数据库工作流 (<%= year %>年) | Bika.ai',
  },
  theme: {
    colorful_theme: '多色主题',
    dark: '深色',
    light: '浅色',
    single_color_gradient_theme: '单色渐变主题',
    system: '跟随系统',
    theme: '主题',
    theme_blue: '蓝色',
    theme_brown: '棕色',
    theme_color_1: '配色一',
    theme_color_2: '配色二',
    theme_color_3: '配色三',
    theme_color_4: '配色四',
    theme_deepPurple: '深紫色',
    theme_green: '绿色',
    theme_indigo: '靛青',
    theme_orange: '橙色',
    theme_pink: '粉色',
    theme_purple: '紫色',
    theme_red: '红色',
    theme_tangerine: '橘色',
    theme_teal: '蓝绿色',
    theme_yellow: '黄色',
  },
  time: {
    hour: '小时',
    minute: '分钟',
  },
  tips: {
    call_agent_recipient_helper: '指定成员使得 ta 跟 Agent 对话',
    drop_files_here: '拖放文件到这里',
    empty: '暂无数据',
    invalid_file_type_error: '无效的文件类型: {invalidFileNames}。可接受的类型: {uploadAccept}',
    no_suitable_resources: '没有合适的资源',
    setting_announcement: '设置公告',
  },
  todo: {
    complete_all: '你已经完成所有待办',
    create: '新建待办',
    create_todo: '创建智能任务',
    finished: '已完成',
    my: '我的',
    my_created: '我创建的',
    no_todo_so_far: '暂无待办事项',
    overdue: '已过期',
    pending: '未完成',
    recent: '最近',
    rejected: '已拒绝',
    today: '今日',
    todo: '待办事项',
    todos: '待办事项',
    unfinished: '未完成',
  },
  toolbar: {
    hide_all: '隐藏所有',
    hide_fileds: '隐藏列',
    hide_kanban_grouping: '隐藏分组',
    previous: '上一个',
    show_all: '显示所有',
  },
  top_up: {
    choose_top_up_amount: '选择充值金额',
    no_balance: '你的余额不足',
    read_and_accept_toc: '我已阅读并接受使用条款',
    top_up: '充值',
    top_up_success: '充值成功',
    your_bika_coins: '你的积分',
  },
  trash: {
    delete: '彻底删除',
    delete_description: '彻底删除后将无法恢复',
    delete_title: '彻底删除条目',
    recover: '恢复',
    recover_description: '此条目将恢复到原路径',
    recover_success: '恢复成功',
    recover_title: '恢复条目',
    trash: '回收站',
  },
  tutorial: '教程',
  unit: {
    pcs: '个',
    row: '行',
    to: {
      admin: '管理员',
      admin_description: '空间站内的所有管理员',
      all_members: '所有成员',
      all_members_description: '空间站内的所有成员',
      current_operator: '当前操作者',
      current_operator_description: '当前操作员',
      email_field: '邮件字段',
      email_field_description: '在数据表的邮件字段中选择邮件',
      email_string: '电子邮件',
      email_subject_and_content: '邮件主题和内容',
      member_field: '表格中的成员字段',
      member_field_description: '在数据表的成员字段中选择成员',
      recipient: '收件人',
      recipient_and_more: '收件人以及更多设置',
      recipient_description: '选择指定的成员、角色或小组',
      role_select_label: '选择角色',
      show_more_options: '显示更多选项',
      specify_members_description: '选择成员、小组或角色',
      specify_units: '成员、小组、角色',
      specify_units_description: '选择成员、小组或角色，不支持选择创建人、修改人',
      team_select_label: '选择小组',
      to_title: '查找',
      unit_member: '成员',
      unit_member_description: '选择指定的一个或多个成员',
      unit_role: '指定角色',
      unit_role_description: '选择指定的一个或多个角色',
      unit_team: '指定小组',
      unit_team_description: '选择指定的一个或多个小组',
      user: '用户',
      user_description: '空间站内的用户，例如创建者、更新者',
      user_select_label: '选择用户',
    },
  },
  unit_selected_modal: {
    empty_data: '数据为空',
    guest: '访客',
    organization: '组织',
    role: '角色',
    selected_team: '已选',
  },
  upgrade: {
    upgrade: '升级',
    upgrade_title: '升级空间站',
    upgrade_to_pro: '升级到 Pro',
    upgrade_to_pro_button: '升级到 Pro',
    upgrade_to_pro_description: '升级到 Pro 解锁更多功能',
  },
  user: {
    about: '关于',
    account: '个人账户',
    avatar: '头像',
    bind_email: '绑定邮箱',
    bind_phone: '绑定手机',
    change_password: '更改密码',
    confirm_password: '确认密码',
    current_password: '当前密码',
    custom_colors: {
      custom: '自定义',
      default: '默认',
      label: '自定义颜色（Beta）',
    },
    debug: '调试',
    download: '下载应用',
    email: '邮箱',
    email_already_bound: '邮箱已被绑定',
    email_bind_success: '绑定成功',
    email_send_success: '发送成功',
    enter_email: '请输入邮箱地址',
    enter_phone: '请输入手机号码',
    enter_verification_code: '请输入验证码',
    get_verification_code: '获取验证码',
    invite_your_friends_to_register_and_get_1000_bk_coins: '邀请你的朋友注册并获得 1,000 积分',
    language: '语言',
    loading: '加载中...',
    member_name: '成员名称',
    name: '昵称',
    new_email: '新邮箱',
    new_password: '新密码',
    nickname: '昵称',
    no_email: '没有邮箱',
    no_name: '无名称',
    password: '密码',
    personal_info: '个人信息',
    personal_settings: '个人设置',
    phone: '手机号码',
    phone_already_bound: '手机号码已被绑定',
    phone_bind_success: '绑定成功',
    phone_send_success: '发送成功',
    preference: '偏好设置',
    profile: '个人信息',
    sessions_current: '当前',
    settings: '设置',
    sign_out: '退出登录',
    theme: '显示模式',
    theme_style: {
      bika: 'Bika',
      dracula: 'Dracula (紫色)',
      label: '主题',
      solarized: 'Solarized (绿色)',
    },
    timezone: '时区',
    update_email: '修改邮箱',
    updated: '更新成功',
    verification_code: '验证码',
    verification_code_send_success: '验证码已发送',
    verify_email: '验证电子邮件',
    website: '网站',
  },
  website: {
    about_bika: '关于 Bika',
    api_doc: 'API 文档',
    architecture_of: '架构图: <%= name %>',
    blog: '博客',
    blog_description: '探索如何使用 Bika.ai 构建您自己的智能 AI 公司，获取实用技巧和见解。',
    blog_title: 'Bika.ai 博客',
    change_region: '切换语言',
    coming_soon: '该功能正在开发中，敬请期待',
    compares: '产品比较',
    contact: {
      contact_discord: '加入 Discord 社区',
      contact_email: '去联系',
      contact_form: '联系销售',
      contact_us: '联系我们',
      discord: 'Discord',
      discourse_community: '社区',
      done_button: '我已联系',
      email: '邮件',
      line: 'LINE',
      more: '更多咨询方式',
      sales: '联系销售',
      scan_code: '请使用微信扫描二维码咨询',
      support: '联系客服',
      via_discord: '加入我们的 Discord 社区',
      via_email: '通过邮箱联系 <EMAIL>',
      via_line: '请扫二维码添加我们的LINE',
      via_sales: '联系我们的销售',
      via_support: '联系服务团队吐槽或报BUG',
      via_wecom: '请扫二维码添加我们的微信',
      wechat: '微信',
    },
    contact_sales: '联系销售',
    contact_service: '联系客服',
    create_template_with_ai: '使用AI创建应用',
    discover: '发现AI应用',
    help: '帮助',
    help_center: '帮助中心',
    help_video: '教学视频',
    install_selfhosted: '安装/下载',
    other_solutions: '更多场景和解决方案',
    price: '价格',
    pricing: {
      description:
        'Bika.ai 提供灵活的定价方案，包括自托管、本地部署和专属云服务，以满足您的特定需求。选择最适合您的方案。',
      title: '定价',
    },
    search_or_build_ai_app_placeholer: '搜索或告诉AI你的需求',
    template: '模板',
    video: {
      marketing: '1.什么是Bika.ai?',
      onboarding: '2. 快速开始',
      product: '3. 深入了解',
    },
    visit_website: '访问官网',
  },
  welcome: {
    explore_templates: '探索AI智能体模板',
    get_started: '开始使用',
    message:
      '很高兴遇见您~ Bika.ai是一个AI组织者，让您可以聊天、构建、管理AI智能体团队，打造您的一人AI公司。',
    mobile_get_started_description: '请访问 {{web}} 使用网页版',
    more: '您可以下载bika.ai的移动应用',
    title: ' 您好，我是Bika',
  },
  widget_config_invalid: '组件配置失效，请重新选择',
  widget_database_has_delete: '绑定的关联表被删除',
  widget_no_data: '无数据',
  widget_settings: {
    add_number: '添加数值',
    add_summary_description: '添加统计值说明',
    add_target_value: '添加目标值',
    all_records: '所有记录',
    chart_option_database_had_been_deleted: '数据表已失效，请重新选择',
    chart_option_field_had_been_deleted: '字段已失效，请重新选择',
    chart_option_view_had_been_deleted: '视图已失效，请重新选择',
    column_dimension_field: '列维度',
    column_dimension_sort_config: '设置列维度的排序',
    exclude_zero_point: '脱离零值比例',
    format_date: '格式化日期',
    jump_link_url: '跳转到关联表',
    more_settings: '更多设置',
    null: '[空]',
    options_config: {
      aggregation_by_field: '统计指定字段',
      ai_write: 'AI 写作',
      asc: '升序',
      avg: '平均值',
      bar_chart: '条形图',
      count_records: '记录总数',
      custom: '自定义',
      database: '内置数据表',
      default: '默认',
      desc: '降序',
      filled: '已填写',
      hidden: '不展示',
      line_chart: '折线图',
      max: '最大值',
      min: '最小值',
      mysql: 'MySQL',
      none: '无',
      not_filled: '未填写',
      percent_empty: '空值占比',
      percent_filled: '已填写占比',
      percent_unique: '唯一值占比',
      pie_chart: '饼图',
      postgresql: 'PostgreSQL',
      sort_by_x: '按 X 排序',
      sort_by_y: '按 Y 排序',
      sum: '求和',
      unique: '唯一值',
      year: '年',
      year_month_day_hyphen: '年-月-日',
      year_month_hyphen: '年-月',
      year_season_hyphen: '年-季度',
      year_week_hyphen: '年-周',
    },
    row_dimension_field: '行维度',
    row_dimension_sort_config: '设置行维度的排序',
    select_axis_sort: '选择一个坐标轴排序',
    select_chart_type: '选择图表类型',
    select_data_source: '选择数据源',
    select_statistics_field: '选择统计字段',
    select_statistics_type: '选择统计类型',
    select_theme_color: '选择主题颜色',
    select_view_source: '选择一个视图作为数据源',
    select_widget_metrics_types: '选择一个字段进行统计',
    select_widget_type: '选择小组件类型',
    separeted_multi_value: '分离多选值',
    set_sort_rules: '设置排序规则',
    show_data_tips: '显示数值标签',
    show_empty_values: '显示维度空值',
    show_totals: '显示总计',
    summary_by_field: '选择一个字段作为统计值',
    widget_Value_x: '数值 (X轴)',
    widget_Value_y: '数值 (Y轴)',
    widget_dimension: '维度 (X轴)',
    widget_name: '组件名称',
    widget_operate_delete: '删除组件',
    widget_url_config: '网址配置',
  },
  widget_title_agenda: '议程',
  widget_title_report: '最近报告',
  widget_title_todo: '最近待办事项',
  wizard: {
    agent_engineer: '智能体工程师',
    ai_wizard: 'AI 向导',
    ai_wizard_description:
      'AI 向导是一款智能引导系统，旨在帮助用户全面了解和掌握产品功能。通过友好的界面和逐步的介绍，AI 向导将引导您熟悉每个模块的使用方法和应用场景。',
    already_freshed: '已刷新',
    an_error_occured: '发生错误',
    are_sure_to_top_up: '确认充值',
    bika_tip1_description:
      'Bika.ai 订阅服务正式开启啦！当前空间站是免费版，已获赠{count}，后续使用 Bika.ai 功能会消耗积分',
    bika_tip1_title: '🚀 会员计划上线',
    bika_tip2_description:
      'Bika.ai 订阅服务正式开启啦！当前空间站是免费版，已获赠 {count}，后续使用 Bika.ai 功能会消耗积分',
    bika_tips1:
      'Bika.ai 因你的热爱与奇思妙想不断成长～现在，我们推出会员计划与功能升级，要把更具突破性的 AI 体验，带给每一位创作者！',
    bika_upgraded_tip2_description:
      '你的邀请码已备好！新用户用你的邀请码注册，你和好友各得{count}奖励 ，快喊小伙伴一起玩～',
    bika_upgraded_tip2_title: '👫 邀请好友，赚积分',
    build_scratch_description: '从零开始创建',
    build_scratch_name: '🏗️ 从零开始构建',
    check_invite_code: '查看邀请码',
    choose_tailor_myself: '对 Bika.ai 有较高了解，希望从零开始定制化构建自己的 AI 智能体和资源',
    close_conversation: '关闭对话',
    contact_label: '您的其它联系方式?如微信、电话、QQ、电子邮箱等?',
    create_space: '创建空间',
    creating: '正在创建',
    credits: '{sum}积分',
    fill_write_code: '请填写有效的邀请码，即可畅享系统全部精彩内容，开启你的专属体验之旅 ~',
    filter_by_columns: '已经根据{Count}列过滤',
    finance: '📊 财务',
    finance_description: '面向资产跟踪、HR简历代理的智能应用',
    get_invite_code: '去社区获取邀请码',
    industry_job_label: '您目前所在的行业是什么？您的职位是什么？',
    installation_completed: '安装完成',
    installer_view_now: '现在预览',
    installer: '安装程序',
    invite_code_added: '邀请填写成功，已获得奖励',
    invite_coin_description: '填写他人的邀请码，双方都可获得{count}',
    marketing: '📣 营销',
    marketing_description:
      '面向营销的智能应用，包括AI写手、幻灯片助手、AI设计师、邮件营销、X(Twitter)社交管理等',
    membership_modal_title: 'Bika.ai 全新升级！',
    mirror_from: '镜像来自',
    my_credits: '个人积分',
    navigate_premium_plan: '了解会员计划',
    new_gift_detail: '感谢你的支持！Bika.ai 额外送上{count} ，快收下这份心意～',
    new_wizard_created: '新的聊天已创建',
    official_website: '官网',
    onboarding: '新手引导',
    one_person_company: '👨‍💻 一人公司',
    one_person_company_description:
      '面向一人公司的智能应用，包括AI写手、幻灯片助手、AI设计师、AI程序员等，',
    placeholder_invite_code: '请填写邀请码',
    please_change_invite_code: '请更换邀请码再试。',
    product: '📋 产品',
    product_description: '面向产品的智能应用，包括AI写手、邮件营销、X(Twitter)社交管理等',
    quick_start: '快速入门',
    row_group_by_columns: '已经根据{Count}列分组',
    sales: '💼 销售',
    sales_description:
      '面向销售的智能应用，包括AI写手、邮件营销、X(Twitter)社交管理、Github Issues创建者等',
    select_role: '请选择您的角色。我们将为您安装预设的Agentic Apps 模板。',
    selected_templates: '即将安装下方所有模板',
    some_columns_hided: '已隐藏{Count}列',
    sorted_by_columns: '已经根据{Count}列排序',
    support: '🤝 支持',
    support_description: '面向支持的智能应用，包括AI写手、邮件营销等',
    topup_space_by_credits: '你当前有{count}，全部充值到「{spaceName}」',
    topup_to_space: '充值到空间站',
    use_case_label: '您希望通过使用 Bika.ai，满足你日常生活工作中的什么使用场景，和解决什么问题?',
    welcome: '欢迎来到 Bika.ai',
    welcome_website: '欢迎来到Bika.ai',
    wizard: '向导',
    wizard_new_user_gift: ' 新用户专属福利！',
    onboarding_role: {
      ceo_name: '👨‍💻 一人公司',
      ceo_description: '一个人就能顶一个团队，既能创作又能执行的高效个体',
      marketer_name: '📢 市场营销',
      marketer_description: '负责产品推广和市场营销的专业人员',
      sales_name: '💼 销售人员',
      sales_description: '负责产品销售和客户关系管理的专业人员',
      product_name: '📋 产品经理',
      product_description: '负责产品规划、设计和开发的专业人员',
      support_name: '🤝 客户支持',
      support_description: '负责客户服务和技术支持的专业人员',
      finance_name: '💰 财务或人力资源',
      finance_description: '负责公司财务管理或人力资源管理的专业人员',
    },
  },
};
export default dict;
