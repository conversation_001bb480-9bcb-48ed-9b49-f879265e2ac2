{"name": "@sandock/domains", "version": "2.0.0-beta.7", "private": false, "type": "module", "main": "index.ts", "types": "index.ts", "exports": {"./prisma": "./prisma/index.ts", "./*": "./*.ts", "./*.tsx": "./*.tsx"}, "scripts": {"test": "dotenv -e ../../apps/sandock/.env.local -- vitest", "lint": "biome format . --write && biome check .", "db:gen": "dotenv -e ../../apps/sandock/.env.local -- prisma generate", "db:migrate": "dotenv -e ../../apps/sandock/.env.local -- prisma migrate dev", "db:deploy": "dotenv -e ../../apps/sandock/.env.local -- prisma migrate deploy", "db:reset": "dotenv -e ../../apps/sandock/.env.local -- prisma migrate reset", "db:reset:force": "dotenv -e ../../apps/sandock/.env.local -- prisma migrate reset --force", "db:studio": "dotenv -e ../../apps/sandock/.env.local -- prisma studio"}, "dependencies": {"@kubernetes/client-node": "1.3.0", "@prisma/client": "6.0.1", "basenext": "workspace:*", "dockerode": "4.0.7", "redis": "^5.8.2", "tar-stream": "^3.1.7"}, "devDependencies": {"@types/dockerode": "^3.3.43", "@types/tar-stream": "^3.1.4", "prisma": "6.0.1", "typescript": "^5.8.3"}}